[input]
input_method = enhanced_ard

[movement]
movement_smoothing_factor = 0.80
vertical_speed_multiplier = 1.0
adaptive_movement_enabled = true
far_speed_multiplier = 0.4
medium_speed_multiplier = 0.3
near_speed_multiplier = 0.2
close_speed_multiplier = 0.1
control_method = pid
pid_kp = 0.30
pid_ki = 0.002
pid_kd = 0.025
pid_integral_limit = 50.0
pid_max_output = 30.0
autonomous_control_enabled = true
approach_distance_threshold = 35.0
decelerate_distance_threshold = 12.0
precision_distance_threshold = 3.0
approach_speed_multiplier = 0.8
decelerate_speed_multiplier = 0.3
precision_speed_multiplier = 0.08
phase_transition_threshold = 0.3
precision_hold_time = 0.2
debug_movement = true
debug_pid = false
velocity_scale_x = 1.2
velocity_scale_y = 1.0
dynamic_speed_boost = 0.8
deceleration_zone = 15
fine_tune_zone = 5
targeting_velocity_x = 0.3
targeting_velocity_y = 0.2
proximity_boost_factor = 2.5
transition_zone_radius = 15
enhanced_pid_kp = 1.2
enhanced_pid_ki = 0.05
enhanced_pid_kd = 0.3
speed_factor = 0.29570469798657717
max_move_distance = 10.0
smoothing_factor = 0.8
acceleration_enabled = true
deceleration_enabled = true
min_move_threshold = 2.0
movement_smoothing_steps = 1

[capture]
capture_region_x = 640
capture_region_y = 640

[enhancement]
display_center_offset = 0
target_height_ratio = 0.20
movement_speed = 1.00
input_method = enhanced_ard

[device]
device_ip = 0.0.0.0
device_port = 50256
serial_port = COM1

[detection]
detection_mode = yolo
performance_priority = balanced
auto_setup_model = false

[performance]
max_fps = 60
cache_size = 50
cache_ttl = 0.05
detection_history_length = 5
movement_history_length = 5
enable_frame_skipping = true
skip_ratio = 0.3
adaptive_skipping = true
target_real_fps = 90
enable_fps_monitoring = true

[visual_color]
target_grouping_threshold = 3, 3
color_range_upper = 115, 255, 255
color_range_lower = 95, 150, 150

[visual_yolo]
model_type = 三角洲v8n.onnx
model_format = auto
model_input_size = 256
model_selection_strategy = prefer_onnx
version_preference = stable
performance_priority = balanced
fallback_behavior = flexible
scan_subdirectories = false
validate_models = true
cache_model_info = true
confidence_threshold = 0.50
nms_threshold = 0.5023529411764707
target_classes = [0]
use_gpu = true
batch_size = 1
target_priority = closest
max_target_distance = 200.0
position_threshold = 20.0
smoothing_enabled = true
model_cache_size = 1
cache_size = 50
cache_ttl = 0.1
smoothing_factor = 0.3
enable_anti_detection = true
randomize_timing = true
cache_randomization = true
half_precision = true
use_tensorrt = true
use_optimized_runtime = true
intra_op_threads = 6
inter_op_threads = 3
enable_mem_pattern = true
execution_mode = parallel
gpu_mem_limit = **********
cudnn_conv_algo_search = exhaustive
warmup_runs = 3
enable_profiling = false
optimize_for_inference = true
max_detections = 20
fast_nms = true
input_size = 320
use_tensorrt_provider = true
tensorrt_priority = highest
use_tensorrt_engine = true
enable_async_inference = true
inference_thread_count = 2
batch_processing = true

[visual_common]
capture_region_x = 256
capture_region_y = 256
processing_region_x = 256
processing_region_y = 256
max_frames_per_sec = 60
auto_detect_resolution = true
resolution_x = 1920
resolution_y = 1080

[compensation]
mode = move
compensation_x = 1.6
compensation_y = 200.0
max_offset = 500
recovery_rate = 0.85
delay_before_compensation = 111
enhanced_compensation_multiplier = 2.5
light_compensation_factor = 0.00

[action]
action_delay = 10
action_randomization = 0
action_threshold = 20

[input_rate]
target_rate = 10

[keys]
key_reload_settings = 0x70
key_toggle_enhancement = 0x71
key_toggle_compensation = 0x72
key_system_exit = 0x73
key_auto_action = 0x51
key_rapid_input = 0x05
enhancement_activation_keys = 0x06, 0x02

[debug]
enabled = true
always_on = true
display_mode = game
enable_performance_monitor = true
fps_display = true
performance_log = true
window_width = 640
window_height = 480
window_position_x = 100
window_position_y = 100
show_detection_boxes = true
show_confidence_scores = true
show_fps_counter = true

[system_optimization]
gpu_mem_limit = **********
enable_memory_monitoring = true
memory_pool_size = **********
enable_memory_growth = true
memory_fraction = 0.8
clear_cache_interval = 100
enable_tensorrt = true
trt_max_workspace_size = 2147483648
trt_fp16_enable = true
trt_int8_enable = false
trt_engine_cache_enable = true
trt_engine_cache_path = ./tensorrt_cache
preload_model = true
cache_model_in_memory = true
use_model_pool = true
model_cache_size = 2
warmup_runs = 3
use_multithreading = true
intra_op_threads = 4
inter_op_threads = 2
inference_thread_count = 2
target_real_fps = 60
quality_vs_speed = balanced
enable_quality_monitoring = true

[tyolo_optimization]
enable_tyolo_engine = false
use_multithreading = true
use_mss_capture = true
use_tensorrt_engine = false
frame_queue_size = 5
detection_queue_size = 3
display_queue_size = 2

[kmboxnet]
host = *************
port = 8868
mac = 63E4E04E

[enhanced_ard]
ard_serial_port = COM18
ard_baud_rate = 115200
hardware_type = github_ard
enable_mouse_passthrough = true
enable_python_control_mode = true
ard_movement_algorithm = direct
ard_smoothing_enabled = false
ard_bezier_enabled = false
ard_pid_kp = 0.8
ard_pid_ki = 0.05
ard_pid_kd = 0.2
dual_device_load_balance = true
dual_device_failover = true
dual_device_auto_recovery = true
dual_device_distance_far = 50
dual_device_distance_near = 10
dual_device_response_threshold = 0.01
ard_humanization_enabled = true
ard_randomization_interval = 50
ard_micro_jitter = true
ard_buffer_size = 16
ard_max_queue_size = 100
ard_worker_threads = 2
stealth_profile = logitech_g502
behavior_pattern = gaming_optimized
stealth_level = high
silent_mode = true

