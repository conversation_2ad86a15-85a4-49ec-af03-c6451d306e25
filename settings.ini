[input]
input_method = enhanced_ard

[enhancement]
input_method = enhanced_ard

[movement]
movement_smoothing_factor = 0.80
vertical_speed_multiplier = 1.0
adaptive_movement_enabled = true
far_speed_multiplier = 0.4
medium_speed_multiplier = 0.3
near_speed_multiplier = 0.2
close_speed_multiplier = 0.1
control_method = pid
pid_kp = 0.30
pid_ki = 0.002
pid_kd = 0.025
pid_integral_limit = 50.0
pid_max_output = 30.0
autonomous_control_enabled = true
approach_distance_threshold = 35.0
decelerate_distance_threshold = 12.0
precision_distance_threshold = 3.0
approach_speed_multiplier = 0.8
decelerate_speed_multiplier = 0.3
precision_speed_multiplier = 0.08
phase_transition_threshold = 0.3
precision_hold_time = 0.2
debug_movement = true
debug_pid = false
velocity_scale_x = 1.2
velocity_scale_y = 1.0
dynamic_speed_boost = 0.8
deceleration_zone = 15
fine_tune_zone = 5
targeting_velocity_x = 0.3
targeting_velocity_y = 0.2
proximity_boost_factor = 2.5
transition_zone_radius = 15
enhanced_pid_kp = 1.2
enhanced_pid_ki = 0.05
enhanced_pid_kd = 0.3
speed_factor = 0.29570469798657717
max_move_distance = 10.0
smoothing_factor = 0.8
acceleration_enabled = true
deceleration_enabled = true
min_move_threshold = 2.0
movement_smoothing_steps = 1

# 预判功能配置
prediction_enabled = true
prediction_strength = 0.15
prediction_distance_threshold = 30.0
prediction_velocity_threshold = 5.0
prediction_static_reduction = 0.3
prediction_max_factor = 0.8

[enhanced_ard]
hardware_type = simple_ard
ard_serial_port = COM18
ard_baud_rate = 57600

[compensation]
compensation_y = 20.9

