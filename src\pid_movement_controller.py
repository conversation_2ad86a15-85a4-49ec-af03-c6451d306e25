#!/usr/bin/env python3
"""
PID控制器实现 - 用于精确移动控制，防止overshoot
可选升级方案，提供更精确的控制
"""

import time
import math
from typing import Tuple, Optional


class PIDController:
    """简化的PID控制器，专门用于移动控制"""
    
    def __init__(self, kp: float = 0.8, ki: float = 0.1, kd: float = 0.2):
        """
        初始化PID控制器
        
        Args:
            kp: 比例增益 (Proportional gain)
            ki: 积分增益 (Integral gain)  
            kd: 微分增益 (Derivative gain)
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        
        # PID状态变量
        self.prev_error_x = 0.0
        self.prev_error_y = 0.0
        self.integral_x = 0.0
        self.integral_y = 0.0
        self.last_time = time.time()
        
        # 积分限制，防止积分饱和
        self.integral_limit = 100.0
        
    def calculate(self, target_x: float, target_y: float) -> Tuple[float, float]:
        """
        计算PID输出
        
        Args:
            target_x: X轴目标位置（误差）
            target_y: Y轴目标位置（误差）
            
        Returns:
            (output_x, output_y): PID控制器输出
        """
        current_time = time.time()
        dt = current_time - self.last_time
        
        if dt <= 0:
            dt = 0.001  # 防止除零
        
        # 误差就是目标位置（因为当前位置是0,0）
        error_x = target_x
        error_y = target_y
        
        # 积分项（累积误差）
        self.integral_x += error_x * dt
        self.integral_y += error_y * dt
        
        # 积分限制，防止积分饱和
        self.integral_x = max(-self.integral_limit, min(self.integral_limit, self.integral_x))
        self.integral_y = max(-self.integral_limit, min(self.integral_limit, self.integral_y))
        
        # 微分项（误差变化率）
        derivative_x = (error_x - self.prev_error_x) / dt
        derivative_y = (error_y - self.prev_error_y) / dt
        
        # PID输出计算
        output_x = (self.kp * error_x + 
                   self.ki * self.integral_x + 
                   self.kd * derivative_x)
        
        output_y = (self.kp * error_y + 
                   self.ki * self.integral_y + 
                   self.kd * derivative_y)
        
        # 更新状态
        self.prev_error_x = error_x
        self.prev_error_y = error_y
        self.last_time = current_time
        
        return output_x, output_y
    
    def reset(self):
        """重置PID控制器状态"""
        self.prev_error_x = 0.0
        self.prev_error_y = 0.0
        self.integral_x = 0.0
        self.integral_y = 0.0
        self.last_time = time.time()
    
    def set_gains(self, kp: float, ki: float, kd: float):
        """动态调整PID参数"""
        self.kp = kp
        self.ki = ki
        self.kd = kd


class PIDMovementController:
    """基于PID控制器的移动控制器"""
    
    def __init__(self, config):
        """初始化PID移动控制器"""
        self.config = config
        
        # PID参数从配置读取
        kp = getattr(config, 'pid_kp', 0.8)
        ki = getattr(config, 'pid_ki', 0.1)
        kd = getattr(config, 'pid_kd', 0.2)
        
        self.pid_controller = PIDController(kp, ki, kd)
        
        # 输出限制
        self.max_output = getattr(config, 'pid_max_output', 50.0)
        
        # 移动历史
        self.movement_x = 0.0
        self.movement_y = 0.0
        
    def calculate_movement(self, enhancement_active: bool, target_info: Optional[Tuple]) -> None:
        """使用PID控制器计算移动"""
        if not enhancement_active or target_info is None:
            self.movement_x = 0.0
            self.movement_y = 0.0
            self.pid_controller.reset()
            return
        
        target_x, target_y = target_info[:2]
        
        # PID计算
        output_x, output_y = self.pid_controller.calculate(target_x, target_y)
        
        # 输出限制
        output_x = max(-self.max_output, min(self.max_output, output_x))
        output_y = max(-self.max_output, min(self.max_output, output_y))
        
        # 应用移动速度缩放
        movement_speed = getattr(self.config, 'movement_speed', 1.0)
        self.movement_x = output_x * movement_speed
        self.movement_y = output_y * movement_speed
        
        # 调试输出
        if getattr(self.config, 'debug_pid', False):
            distance = math.sqrt(target_x**2 + target_y**2)
            print(f"🎯 PID控制: 距离={distance:.1f}, 输入=({target_x:.1f},{target_y:.1f}), 输出=({output_x:.2f},{output_y:.2f})")
    
    def get_movement_delta(self) -> Tuple[float, float]:
        """获取移动增量"""
        return self.movement_x, self.movement_y
    
    def reset_movement_delta(self) -> None:
        """重置移动增量"""
        self.movement_x = 0.0
        self.movement_y = 0.0
    
    def update_config(self, new_config) -> None:
        """更新配置"""
        self.config = new_config
        
        # 更新PID参数
        kp = getattr(new_config, 'pid_kp', 0.8)
        ki = getattr(new_config, 'pid_ki', 0.1)
        kd = getattr(new_config, 'pid_kd', 0.2)
        
        self.pid_controller.set_gains(kp, ki, kd)
        self.max_output = getattr(new_config, 'pid_max_output', 50.0)


# PID参数调优指南
"""
PID参数调优建议：

1. **Kp (比例增益)**:
   - 控制响应速度
   - 过高: 快速但可能振荡
   - 过低: 响应慢
   - 推荐范围: 0.5-1.2

2. **Ki (积分增益)**:
   - 消除稳态误差
   - 过高: 可能导致振荡
   - 过低: 无法精确到位
   - 推荐范围: 0.05-0.2

3. **Kd (微分增益)**:
   - 预测和阻尼
   - 过高: 对噪声敏感
   - 过低: 可能overshoot
   - 推荐范围: 0.1-0.3

调优步骤：
1. 先设置Ki=0, Kd=0，只调Kp
2. 找到Kp的临界值（开始振荡的点）
3. 设置Kp为临界值的60%
4. 逐步增加Kd直到振荡消失
5. 最后微调Ki消除稳态误差
"""
