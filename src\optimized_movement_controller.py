#!/usr/bin/env python3
"""
优化的移动控制器 - 基于RookieAI和Axiom-AI的最佳实践
解决移动速度慢、跟不上目标的问题
"""

import time
import math
from typing import Tuple, Optional


class EnhancedPIDController:
    """增强型PID控制器 - 高性能响应算法"""

    def __init__(self, kp: float = 1.2, ki: float = 0.05, kd: float = 0.3):
        """
        初始化快速PID控制器
        
        Args:
            kp: 比例增益 (更高的响应速度)
            ki: 积分增益 (较小，避免振荡)
            kd: 微分增益 (适中，提供阻尼)
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        
        # 状态变量
        self.prev_error = 0.0
        self.integral = 0.0
        self.last_time = time.time()
        
        # 积分限制
        self.integral_limit = 50.0
    
    def update(self, error: float) -> float:
        """更新PID控制器"""
        current_time = time.time()
        dt = current_time - self.last_time
        
        if dt <= 0:
            dt = 0.001  # 防止除零
        
        # 积分项
        self.integral += error * dt
        self.integral = max(-self.integral_limit, min(self.integral_limit, self.integral))
        
        # 微分项
        derivative = (error - self.prev_error) / dt
        
        # PID输出
        output = (self.kp * error + 
                 self.ki * self.integral + 
                 self.kd * derivative)
        
        # 更新状态
        self.prev_error = error
        self.last_time = current_time
        
        return output
    
    def reset(self):
        """重置PID状态"""
        self.prev_error = 0.0
        self.integral = 0.0
        self.last_time = time.time()


class AdvancedMovementController:
    """高级移动控制器 - 智能响应算法"""

    def __init__(self, config):
        """初始化高级移动控制器"""
        self.config = config

        # 创建独立的X轴和Y轴增强PID控制器
        self.enhanced_pid_x = EnhancedPIDController(
            kp=getattr(config, 'enhanced_pid_kp', 1.2),
            ki=getattr(config, 'enhanced_pid_ki', 0.05),
            kd=getattr(config, 'enhanced_pid_kd', 0.3)
        )

        self.enhanced_pid_y = EnhancedPIDController(
            kp=getattr(config, 'enhanced_pid_kp', 1.2),
            ki=getattr(config, 'enhanced_pid_ki', 0.05),
            kd=getattr(config, 'enhanced_pid_kd', 0.3)
        )
        
        # 智能移动参数
        self.velocity_scale_x = getattr(config, 'velocity_scale_x', 1.0)
        self.velocity_scale_y = getattr(config, 'velocity_scale_y', 1.0)
        self.dynamic_speed_boost = getattr(config, 'dynamic_speed_boost', 2.0)

        # 智能分区参数
        self.deceleration_zone = getattr(config, 'deceleration_zone', 15)
        self.fine_tune_zone = getattr(config, 'fine_tune_zone', 5)
        
        # 性能优化
        self.last_target_x = 0.0
        self.last_target_y = 0.0
        self.movement_x = 0.0
        self.movement_y = 0.0
        
        # 调试
        self.debug_enabled = getattr(config, 'debug_movement', False)
    
    def calculate_movement(self, enhancement_active: bool, target_info: Optional[Tuple]) -> None:
        """
        计算移动量 - 优化版本
        
        Args:
            enhancement_active: 是否激活增强功能
            target_info: 目标信息 (x, y, width, height)
        """
        # 快速退出条件
        if not enhancement_active or target_info is None:
            self._reset_movement()
            return
        
        target_x, target_y = target_info[:2]
        
        # 计算距离（用于速度调整）
        distance = math.sqrt(target_x**2 + target_y**2)
        
        # 使用增强PID控制器计算基础移动量
        move_x = self.enhanced_pid_x.update(target_x)
        move_y = self.enhanced_pid_y.update(target_y)

        # 应用智能速度调整算法
        velocity_boost_x, velocity_boost_y = self._calculate_velocity_boost(distance)

        # 最终移动量计算
        self.movement_x = move_x * self.velocity_scale_x * velocity_boost_x
        self.movement_y = move_y * self.velocity_scale_y * velocity_boost_y
        
        # 应用移动限制
        self._apply_movement_limits()
        
        # 调试输出
        if self.debug_enabled:
            print(f"🚀 优化移动: 距离={distance:.1f}, PID=({move_x:.2f},{move_y:.2f}), "
                  f"倍率=({speed_multiplier_x:.2f},{speed_multiplier_y:.2f}), "
                  f"最终=({self.movement_x:.2f},{self.movement_y:.2f})")
    
    def _calculate_velocity_boost(self, distance: float) -> Tuple[float, float]:
        """
        计算智能速度增强倍率

        Args:
            distance: 到目标的距离

        Returns:
            (velocity_boost_x, velocity_boost_y): X轴和Y轴速度增强倍率
        """
        # 基于距离的智能速度调整算法
        if distance > 50:
            # 远距离：高速接近模式
            boost_x = self.dynamic_speed_boost * 1.2  # 更快的远距离移动
            boost_y = self.dynamic_speed_boost * 1.0
        elif distance > 25:
            # 中距离：标准速度模式
            boost_x = self.dynamic_speed_boost
            boost_y = self.dynamic_speed_boost * 0.9
        elif distance > self.deceleration_zone:
            # 接近减速区：渐进减速模式
            ratio = (distance - self.deceleration_zone) / (25 - self.deceleration_zone)
            boost_x = 0.8 + ratio * 0.4  # 0.8-1.2范围
            boost_y = 0.7 + ratio * 0.3  # 0.7-1.0范围
        elif distance > self.fine_tune_zone:
            # 减速区：精确控制模式
            ratio = (distance - self.fine_tune_zone) / (self.deceleration_zone - self.fine_tune_zone)
            boost_x = 0.4 + ratio * 0.4  # 0.4-0.8范围
            boost_y = 0.3 + ratio * 0.4  # 0.3-0.7范围
        else:
            # 精确区：微调模式
            boost_x = 0.3
            boost_y = 0.2

        return boost_x, boost_y
    
    def _apply_movement_limits(self):
        """应用移动限制"""
        # 最大移动限制
        max_move = getattr(self.config, 'max_movement_per_frame', 100.0)
        
        # 限制移动范围
        self.movement_x = max(-max_move, min(max_move, self.movement_x))
        self.movement_y = max(-max_move, min(max_move, self.movement_y))
        
        # 最小移动阈值（避免微小抖动）
        min_move = getattr(self.config, 'min_movement_threshold', 0.5)
        if abs(self.movement_x) < min_move:
            self.movement_x = 0.0
        if abs(self.movement_y) < min_move:
            self.movement_y = 0.0
    
    def _reset_movement(self):
        """重置移动状态"""
        self.movement_x = 0.0
        self.movement_y = 0.0
        self.enhanced_pid_x.reset()
        self.enhanced_pid_y.reset()

    def get_movement_delta(self) -> Tuple[float, float]:
        """获取移动增量"""
        return self.movement_x, self.movement_y

    def update_enhanced_pid_parameters(self, kp: float, ki: float, kd: float):
        """动态更新增强PID参数"""
        self.enhanced_pid_x.kp = kp
        self.enhanced_pid_x.ki = ki
        self.enhanced_pid_x.kd = kd

        self.enhanced_pid_y.kp = kp
        self.enhanced_pid_y.ki = ki
        self.enhanced_pid_y.kd = kd

        if self.debug_enabled:
            print(f"🎯 增强PID参数更新: Kp={kp}, Ki={ki}, Kd={kd}")

    def set_velocity_parameters(self, velocity_scale_x: float, velocity_scale_y: float, dynamic_boost: float):
        """设置速度参数"""
        self.velocity_scale_x = velocity_scale_x
        self.velocity_scale_y = velocity_scale_y
        self.dynamic_speed_boost = dynamic_boost

        if self.debug_enabled:
            print(f"🚀 速度参数更新: 速度缩放=({velocity_scale_x}, {velocity_scale_y}), 动态增强={dynamic_boost}")


class StreamlinedController:
    """流线型移动控制器 - 极致性能优化"""

    def __init__(self, config):
        """初始化流线型控制器"""
        self.config = config

        # 流线型参数
        self.targeting_velocity_x = getattr(config, 'targeting_velocity_x', 0.8)
        self.targeting_velocity_y = getattr(config, 'targeting_velocity_y', 0.6)
        self.proximity_boost_factor = getattr(config, 'proximity_boost_factor', 2.0)
        self.transition_zone_radius = getattr(config, 'transition_zone_radius', 15)
        
        # 移动状态
        self.movement_x = 0.0
        self.movement_y = 0.0
        
        # 调试
        self.debug_enabled = getattr(config, 'debug_movement', False)
    
    def calculate_movement(self, enhancement_active: bool, target_info: Optional[Tuple]) -> None:
        """
        流线型移动计算 - 极致性能优化

        Args:
            enhancement_active: 是否激活增强功能
            target_info: 目标信息 (x, y, width, height)
        """
        # 快速退出
        if not enhancement_active or target_info is None:
            self.movement_x = 0.0
            self.movement_y = 0.0
            return

        target_x, target_y = target_info[:2]
        distance = math.sqrt(target_x**2 + target_y**2)

        # 流线型动态速度计算
        if distance < self.transition_zone_radius:
            # 近距离：使用距离比例调整速度
            velocity_ratio = distance / self.transition_zone_radius
            current_velocity_x = self.targeting_velocity_x + (self.proximity_boost_factor - 1) * self.targeting_velocity_x * velocity_ratio
            current_velocity_y = self.targeting_velocity_y + (self.proximity_boost_factor - 1) * self.targeting_velocity_y * velocity_ratio
        else:
            # 远距离：使用最大速度
            current_velocity_x = self.targeting_velocity_x * self.proximity_boost_factor
            current_velocity_y = self.targeting_velocity_y * self.proximity_boost_factor

        # 直接计算移动量（流线型方式）
        self.movement_x = target_x * current_velocity_x
        self.movement_y = target_y * current_velocity_y

        # 调试输出
        if self.debug_enabled:
            print(f"⚡ 流线型移动: 距离={distance:.1f}, 速度=({current_velocity_x:.2f},{current_velocity_y:.2f}), "
                  f"移动=({self.movement_x:.2f},{self.movement_y:.2f})")
    
    def get_movement_delta(self) -> Tuple[float, float]:
        """获取移动增量"""
        return self.movement_x, self.movement_y
