"""
    Movement Controller - Advanced cursor movement and compensation system
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import math
import win32api
import numpy as np
import sys
import os
from typing import Optional, Tuple, Dict, Any
from behavior_randomizer import BehaviorRandomizer
from optimized_movement_controller import AdvancedMovementController, StreamlinedController

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
try:
    from utils.base_configurable import PerformanceConfigurable
    from utils.performance_utils import PerformanceMonitor, UnifiedExceptionHandler
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False


class MovementController(PerformanceConfigurable if UTILS_AVAILABLE else object):
    """Advanced cursor movement and compensation controller."""

    def __init__(self, config):
        """Initialize the movement controller."""
        # 初始化基础配置管理
        if UTILS_AVAILABLE:
            super().__init__(config)
            self.performance_monitor = PerformanceMonitor()
            self.exception_handler = UnifiedExceptionHandler("movement_controller")
        else:
            self.config = config

        self.movement_x = 0.0
        self.movement_y = 0.0
        self.previous_x = 0.0
        self.previous_y = 0.0

        # Enhanced behavior simulation
        self.behavior_randomizer = BehaviorRandomizer()

        # Movement history for advanced smoothing
        self.movement_history = []
        self.max_history_length = getattr(config, 'movement_history_length', 5)

        # Adaptive parameters
        self.adaptive_smoothing = getattr(config, 'movement_smoothing_factor', 0.3)
        self.last_target_time = time.time()

        # Autonomous control state for overshoot prevention
        self.autonomous_control_enabled = getattr(config, 'autonomous_control_enabled', True)
        self.last_distance = 0.0
        self.movement_phase = "approach"  # approach, decelerate, precision
        self.phase_transition_time = time.time()
        self.consecutive_precision_count = 0
        self.enhancement_duration = 0.0

        # 控制器选择和初始化 - 简化版本
        self.control_method = getattr(config, 'control_method', 'streamlined')  # 只支持 'streamlined', 'pid'
        self.pid_enabled = (self.control_method == 'pid')
        self._init_controllers()

    def _init_controllers(self):
        """初始化移动控制器 - 简化版本"""
        # 只初始化必要的控制器
        if self.control_method == 'streamlined':
            self.streamlined_controller = StreamlinedController(self.config)
        elif self.control_method == 'pid':
            self._init_pid_controller()

        print(f"🎮 移动控制器初始化: 方法={self.control_method}")

    def calculate_movement(self, enhancement_active: bool, target_info: Optional[Tuple]) -> None:
        """Calculate cursor movement based on target information."""
        start_time = time.time()

        try:
            # 如果没有激活增强功能，立即重置所有移动状态
            if not enhancement_active:
                self._reset_movement_state()
                return

            # 如果没有目标信息，也重置移动状态
            if target_info is None:
                self._reset_movement_state()
                return

            target_x, target_y = target_info[:2]
            current_time = time.time()

            # Apply autonomous control for overshoot prevention
            base_movement_speed = getattr(self.config, 'movement_speed', 1.0)
            vertical_multiplier = getattr(self.config, 'vertical_speed_multiplier', 0.8)

            # Calculate distance
            distance = math.sqrt(target_x**2 + target_y**2)

            # 简化的控制方法选择：只支持streamlined和pid
            if self.control_method == 'streamlined':
                # 使用流线型控制器
                self.streamlined_controller.calculate_movement(enhancement_active, target_info)
                scaled_x, scaled_y = self.streamlined_controller.get_movement_delta()
                movement_phase = "streamlined"

            elif self.control_method == 'pid':
                # PID控制方法
                pid_output_x, pid_output_y = self._calculate_pid_output(target_x, target_y, current_time)
                scaled_x = pid_output_x * base_movement_speed
                scaled_y = pid_output_y * base_movement_speed * vertical_multiplier
                movement_phase = "pid"

                # PID调试输出
                if hasattr(self.config, 'debug_movement') and self.config.debug_movement:
                    print(f"🎯 PID控制: 距离={distance:.1f}, PID输出=({pid_output_x:.2f},{pid_output_y:.2f}), 最终移动=({scaled_x:.2f},{scaled_y:.2f})")
            else:
                # 默认使用streamlined
                print(f"⚠️ 未知控制方法 {self.control_method}，使用streamlined")
                if not hasattr(self, 'streamlined_controller'):
                    self.streamlined_controller = StreamlinedController(self.config)
                self.streamlined_controller.calculate_movement(enhancement_active, target_info)
                scaled_x, scaled_y = self.streamlined_controller.get_movement_delta()
                movement_phase = "streamlined"

                # 自适应控制调试输出
                if hasattr(self.config, 'debug_movement') and self.config.debug_movement:
                    print(f"🎯 自适应控制: 距离={distance:.1f}, 相位={movement_phase}, 速度={adaptive_speed:.2f}")

            # Apply adaptive smoothing
            smoothing_factor = self._get_adaptive_smoothing(current_time)

            # 修复：移除小幅移动的过度平滑，恢复正常响应速度
            # 使用统一的平滑强度，避免小幅移动变得极慢
            adjusted_smoothing = smoothing_factor

            # 可选：仅对极小移动(<1像素)稍微增加平滑
            movement_distance = (scaled_x**2 + scaled_y**2)**0.5
            if movement_distance < 1:
                adjusted_smoothing = min(0.7, smoothing_factor * 1.2)  # 轻微增加而非1.5倍

            # Calculate smoothed movement
            smoothed_x = (1 - adjusted_smoothing) * self.previous_x + adjusted_smoothing * scaled_x
            smoothed_y = (1 - adjusted_smoothing) * self.previous_y + adjusted_smoothing * scaled_y

            # 减少额外的行为变化以避免抖动
            # 注释掉额外的抖动，使用更稳定的移动
            # final_x, final_y = self.behavior_randomizer.add_movement_jitter(smoothed_x, smoothed_y)
            final_x, final_y = smoothed_x, smoothed_y

            # Apply movement prediction for better tracking - 仅在有目标时应用预测
            if target_info is not None:
                final_x, final_y = self._apply_movement_prediction(final_x, final_y)

            # Store calculated values
            self.previous_x, self.previous_y = smoothed_x, smoothed_y
            self.movement_x, self.movement_y = final_x, final_y

            # Update movement history
            self._update_movement_history(final_x, final_y)
            self.last_target_time = current_time

        except Exception as e:
            if UTILS_AVAILABLE and hasattr(self, 'exception_handler'):
                self.exception_handler.handle_exception(e, "movement calculation")
        finally:
            # 记录性能数据
            if UTILS_AVAILABLE and hasattr(self, 'performance_monitor'):
                calc_time = time.time() - start_time
                self.performance_monitor.record_frame_time(calc_time)
        
    def apply_compensation(self, compensation_active: bool, delta_time: float) -> None:
        """
        旧的后坐力补偿方法 - 已被智能后坐力控制器替代
        保留此方法以维持兼容性，但功能已移至RecoilController
        """
        # 🔧 此方法已被智能后坐力控制器替代
        # 新的后坐力控制逻辑在 display_helper.py 中的 RecoilController 中处理
        pass
                    
    def get_movement_delta(self) -> Tuple[float, float]:
        """Get the calculated movement delta."""
        return self.movement_x, self.movement_y
        
    def reset_movement_delta(self) -> None:
        """Reset movement delta values."""
        self.movement_x = 0.0
        self.movement_y = 0.0
        
    def get_offset_compensation(self) -> float:
        """Get current offset compensation value (已弃用，仅保持兼容性)."""
        return 0.0

    def update_config(self, new_config) -> None:
        """Update movement controller configuration without recreating the instance."""
        # 使用基础配置管理
        if UTILS_AVAILABLE and hasattr(self, 'update_config'):
            super().update_config(new_config)
        else:
            self.config = new_config

        # Update adaptive parameters if they changed
        self.adaptive_smoothing = getattr(new_config, 'movement_smoothing_factor', 0.3)
        self.max_history_length = getattr(new_config, 'movement_history_length', 5)

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            'movement_history_length': len(self.movement_history),
            'max_history_length': self.max_history_length,
            'adaptive_smoothing': self.adaptive_smoothing,
            'last_movement': {
                'x': self.movement_x,
                'y': self.movement_y
            }
        }

        # 性能监控统计
        if UTILS_AVAILABLE and hasattr(self, 'performance_monitor'):
            stats['performance'] = self.performance_monitor.get_stats()

        # 错误统计
        if UTILS_AVAILABLE and hasattr(self, 'exception_handler'):
            stats['errors'] = self.exception_handler.get_error_stats()

        return stats
        
    def _reset_movement_state(self) -> None:
        """Reset movement state when no target is present."""
        # Gradually reduce previous values to zero for natural deceleration
        decay_factor = 0.8
        self.previous_x *= decay_factor
        self.previous_y *= decay_factor
        
        # Clear movement if values are very small
        if abs(self.previous_x) < 0.1:
            self.previous_x = 0.0
        if abs(self.previous_y) < 0.1:
            self.previous_y = 0.0
            
        # 重置当前移动值，确保没有残留移动
        self.movement_x = 0.0
        self.movement_y = 0.0
        
        # 重置移动历史和距离，避免使用过时数据
        self.movement_history.clear()
        self.last_distance = 0.0
        
    def _calculate_adaptive_speed(self, target_x: float, target_y: float, base_speed: float) -> tuple[float, float]:
        """Calculate adaptive movement speed based on distance to prevent overshoot."""
        import math

        # Calculate distance to target
        distance = math.sqrt(target_x**2 + target_y**2)

        # Get adaptive movement settings
        adaptive_enabled = getattr(self.config, 'adaptive_movement_enabled', True)

        if not adaptive_enabled:
            return base_speed, distance

        # Distance-based speed multipliers to prevent overshoot
        if distance > 50:    # Far distance: fast approach
            multiplier = getattr(self.config, 'far_speed_multiplier', 1.2)
        elif distance > 20:  # Medium distance: normal speed
            multiplier = getattr(self.config, 'medium_speed_multiplier', 1.0)
        elif distance > 8:   # Near distance: start slowing down
            multiplier = getattr(self.config, 'near_speed_multiplier', 0.6)
        elif distance > 3:   # Very close: significant slowdown
            multiplier = getattr(self.config, 'close_speed_multiplier', 0.3)
        else:               # Precision range: micro adjustments
            multiplier = getattr(self.config, 'precision_speed_multiplier', 0.1)

        adaptive_speed = base_speed * multiplier
        return adaptive_speed, distance

    def _autonomous_movement_control(self, target_x: float, target_y: float, base_speed: float, distance: float, current_time: float) -> tuple[float, str]:
        """
        自主移动控制算法 - 智能防止overshoot

        Args:
            target_x, target_y: 目标位置
            base_speed: 基础移动速度
            distance: 到目标的距离
            current_time: 当前时间

        Returns:
            (adaptive_speed, movement_phase): 自适应速度和当前移动相位
        """
        # 获取自主控制配置
        phase_transition_threshold = getattr(self.config, 'phase_transition_threshold', 0.5)
        precision_hold_time = getattr(self.config, 'precision_hold_time', 0.3)

        # 计算距离变化率（接近速度）
        distance_change = self.last_distance - distance if self.last_distance > 0 else 0
        approach_velocity = distance_change / 0.016 if distance_change > 0 else 0  # 假设60fps

        # 智能相位判断
        new_phase = self._determine_movement_phase(distance, approach_velocity, current_time)

        # 相位转换检测
        if new_phase != self.movement_phase:
            self.movement_phase = new_phase
            self.phase_transition_time = current_time
            if new_phase == "precision":
                self.consecutive_precision_count = 0

        # 根据相位计算自适应速度
        adaptive_speed = self._calculate_phase_based_speed(base_speed, distance, approach_velocity)

        # 更新状态
        self.last_distance = distance

        # 精确相位持续时间控制
        if self.movement_phase == "precision":
            self.consecutive_precision_count += 1
            # 如果在精确相位停留过久，可能需要微调策略
            if self.consecutive_precision_count > 30:  # 约0.5秒
                adaptive_speed *= 0.8  # 进一步降低速度

        return adaptive_speed, self.movement_phase

    def _determine_movement_phase(self, distance: float, approach_velocity: float, current_time: float) -> str:
        """
        智能判断当前移动相位

        Args:
            distance: 到目标的距离
            approach_velocity: 接近速度
            current_time: 当前时间

        Returns:
            movement_phase: 'approach', 'decelerate', 'precision'
        """
        # 获取相位阈值配置
        approach_threshold = getattr(self.config, 'approach_distance_threshold', 30.0)
        decelerate_threshold = getattr(self.config, 'decelerate_distance_threshold', 10.0)
        precision_threshold = getattr(self.config, 'precision_distance_threshold', 3.0)

        # 基于距离的基础相位判断
        if distance > approach_threshold:
            base_phase = "approach"
        elif distance > decelerate_threshold:
            base_phase = "decelerate"
        elif distance > precision_threshold:
            base_phase = "precision"
        else:
            base_phase = "precision"

        # 改进的基于接近速度的智能调整 - 修复远距离overshoot
        if approach_velocity > 8.0 and distance > approach_threshold * 0.8:
            # 远距离高速接近时，提前进入减速相位
            return "decelerate"
        elif approach_velocity > 5.0 and distance < decelerate_threshold:
            # 如果接近速度过快，提前进入减速相位
            return "decelerate"
        elif approach_velocity > 2.0 and distance < precision_threshold:
            # 如果在精确范围内速度仍然较快，保持减速相位
            return "decelerate"

        # 相位稳定性检查 - 防止频繁切换
        phase_duration = current_time - self.phase_transition_time
        if phase_duration < 0.1 and self.movement_phase != base_phase:
            # 如果相位切换过于频繁，保持当前相位
            return self.movement_phase

        return base_phase

    def _calculate_phase_based_speed(self, base_speed: float, distance: float, approach_velocity: float) -> float:
        """
        基于移动相位计算自适应速度

        Args:
            base_speed: 基础移动速度
            distance: 到目标的距离
            approach_velocity: 接近速度

        Returns:
            adaptive_speed: 自适应移动速度
        """
        if self.movement_phase == "approach":
            # 接近相位：快速移动，但防止远距离overshoot
            base_multiplier = getattr(self.config, 'approach_speed_multiplier', 1.0)

            # 改进的距离动态调整 - 修复远距离overshoot
            if distance > 50:
                # 超远距离：适中速度，避免过快
                multiplier = base_multiplier * 0.9
            elif distance > 35:
                # 远距离：正常速度
                multiplier = base_multiplier
            elif distance > 25:
                # 接近中距离：开始收敛
                multiplier = base_multiplier * 0.8
            else:
                # 即将进入减速相位：预减速
                multiplier = base_multiplier * 0.6

        elif self.movement_phase == "decelerate":
            # 减速相位：根据接近速度动态减速
            base_multiplier = getattr(self.config, 'decelerate_speed_multiplier', 0.6)

            # 速度越快，减速越多
            if approach_velocity > 3.0:
                velocity_factor = 0.4  # 大幅减速
            elif approach_velocity > 1.5:
                velocity_factor = 0.7  # 中度减速
            else:
                velocity_factor = 1.0  # 正常减速

            multiplier = base_multiplier * velocity_factor

        else:  # precision phase
            # 精确相位：微调移动，防止overshoot
            base_multiplier = getattr(self.config, 'precision_speed_multiplier', 0.15)

            # 根据距离进一步细化
            if distance < 1.0:
                multiplier = base_multiplier * 0.5  # 极精确
            elif distance < 2.0:
                multiplier = base_multiplier * 0.8  # 高精确
            else:
                multiplier = base_multiplier  # 标准精确

        return base_speed * multiplier

    def _get_adaptive_smoothing(self, current_time: float) -> float:
        """Calculate adaptive smoothing factor based on movement patterns."""
        base_smoothing = self.config.movement_smoothing_factor

        # Adjust smoothing based on time since last target
        time_since_target = current_time - self.last_target_time

        if time_since_target > 0.5:  # No target for 500ms
            # Increase smoothing for more stable movement
            adaptive_factor = min(1.5, 1.0 + time_since_target * 0.2)
        else:
            # Normal smoothing for active tracking
            adaptive_factor = 1.0

        # Apply behavioral variation
        final_smoothing = self.behavior_randomizer.get_smoothing_factor(
            base_smoothing * adaptive_factor
        )

        return max(0.0, min(1.0, final_smoothing))

    def set_enhancement_duration(self, duration: float) -> None:
        """设置自瞄激活持续时间，用于自主控制优化"""
        self.enhancement_duration = duration

        # 根据持续时间调整自主控制策略
        if duration > 2.0:  # 长时间持续按键
            # 可能需要更精确的控制
            if hasattr(self.config, 'long_press_precision_boost'):
                self.config.long_press_precision_boost = True
        else:
            if hasattr(self.config, 'long_press_precision_boost'):
                self.config.long_press_precision_boost = False

    def _init_pid_controller(self) -> None:
        """初始化PID控制器"""
        # PID参数
        self.kp = getattr(self.config, 'pid_kp', 0.8)
        self.ki = getattr(self.config, 'pid_ki', 0.1)
        self.kd = getattr(self.config, 'pid_kd', 0.2)

        # PID状态变量
        self.prev_error_x = 0.0
        self.prev_error_y = 0.0
        self.integral_x = 0.0
        self.integral_y = 0.0
        self.last_pid_time = time.time()

        # 积分限制，防止积分饱和
        self.integral_limit = getattr(self.config, 'pid_integral_limit', 100.0)
        self.max_output = getattr(self.config, 'pid_max_output', 50.0)

    def _calculate_pid_output(self, target_x: float, target_y: float, current_time: float) -> tuple[float, float]:
        """
        计算PID控制器输出

        Args:
            target_x, target_y: 目标位置（误差）
            current_time: 当前时间

        Returns:
            (output_x, output_y): PID控制器输出
        """
        dt = current_time - self.last_pid_time
        if dt <= 0:
            dt = 0.001  # 防止除零

        # 误差就是目标位置（因为当前位置是0,0）
        error_x = target_x
        error_y = target_y

        # 积分项（累积误差）
        self.integral_x += error_x * dt
        self.integral_y += error_y * dt

        # 积分限制，防止积分饱和
        self.integral_x = max(-self.integral_limit, min(self.integral_limit, self.integral_x))
        self.integral_y = max(-self.integral_limit, min(self.integral_limit, self.integral_y))

        # 微分项（误差变化率）
        derivative_x = (error_x - self.prev_error_x) / dt
        derivative_y = (error_y - self.prev_error_y) / dt

        # PID输出计算
        output_x = (self.kp * error_x +
                   self.ki * self.integral_x +
                   self.kd * derivative_x)

        output_y = (self.kp * error_y +
                   self.ki * self.integral_y +
                   self.kd * derivative_y)

        # 输出限制
        output_x = max(-self.max_output, min(self.max_output, output_x))
        output_y = max(-self.max_output, min(self.max_output, output_y))

        # 更新状态
        self.prev_error_x = error_x
        self.prev_error_y = error_y
        self.last_pid_time = current_time

        # 调试输出
        if hasattr(self.config, 'debug_pid') and self.config.debug_pid:
            distance = math.sqrt(target_x**2 + target_y**2)
            print(f"🎯 PID调试: 距离={distance:.1f}, P=({self.kp*error_x:.2f},{self.kp*error_y:.2f}), I=({self.ki*self.integral_x:.2f},{self.ki*self.integral_y:.2f}), D=({self.kd*derivative_x:.2f},{self.kd*derivative_y:.2f}), 输出=({output_x:.2f},{output_y:.2f})")

        return output_x, output_y

    def reset_pid_controller(self) -> None:
        """重置PID控制器状态"""
        self.prev_error_x = 0.0
        self.prev_error_y = 0.0
        self.integral_x = 0.0
        self.integral_y = 0.0
        self.last_pid_time = time.time()

    def update_pid_parameters(self, kp: float = None, ki: float = None, kd: float = None) -> None:
        """动态更新PID参数"""
        if kp is not None:
            self.kp = kp
        if ki is not None:
            self.ki = ki
        if kd is not None:
            self.kd = kd

    def switch_control_method(self, method: str) -> None:
        """
        切换控制方法

        Args:
            method: 'adaptive' 或 'pid'
        """
        if method in ['adaptive', 'pid']:
            old_method = self.control_method
            self.control_method = method
            self.pid_enabled = (method == 'pid')

            # 重置控制器状态以避免突然变化
            if method == 'pid':
                self.reset_pid_controller()
            else:
                # 重置自适应控制状态
                self.movement_phase = "approach"
                self.last_distance = 0.0
                self.consecutive_precision_count = 0

            if hasattr(self.config, 'debug_movement') and self.config.debug_movement:
                print(f"🔄 控制方法切换: {old_method} -> {method}")

    def get_control_status(self) -> dict:
        """
        获取当前控制状态信息

        Returns:
            控制状态字典
        """
        status = {
            'control_method': self.control_method,
            'autonomous_control_enabled': self.autonomous_control_enabled,
        }

        if self.control_method == 'pid':
            status.update({
                'pid_kp': self.kp,
                'pid_ki': self.ki,
                'pid_kd': self.kd,
                'pid_integral_x': self.integral_x,
                'pid_integral_y': self.integral_y,
                'pid_prev_error_x': self.prev_error_x,
                'pid_prev_error_y': self.prev_error_y,
            })
        else:
            status.update({
                'movement_phase': getattr(self, 'movement_phase', 'unknown'),
                'last_distance': self.last_distance,
                'consecutive_precision_count': self.consecutive_precision_count,
            })

        return status

    def update_config(self, new_config) -> None:
        """
        更新配置并重新初始化相关参数
        用于热重载功能
        """
        old_control_method = self.control_method
        self.config = new_config

        # 更新控制方法
        self.control_method = getattr(new_config, 'control_method', 'adaptive')
        self.pid_enabled = (self.control_method == 'pid')
        self.autonomous_control_enabled = getattr(new_config, 'autonomous_control_enabled', True)

        # 更新PID参数
        self.kp = getattr(new_config, 'pid_kp', 0.6)
        self.ki = getattr(new_config, 'pid_ki', 0.05)
        self.kd = getattr(new_config, 'pid_kd', 0.15)
        self.integral_limit = getattr(new_config, 'pid_integral_limit', 50.0)
        self.max_output = getattr(new_config, 'pid_max_output', 30.0)

        # 更新自适应控制参数
        self.adaptive_smoothing = getattr(new_config, 'movement_smoothing_factor', 0.3)

        # 如果控制方法发生变化，重置控制器状态
        if old_control_method != self.control_method:
            if self.control_method == 'pid':
                self.reset_pid_controller()
            else:
                # 重置自适应控制状态
                self.movement_phase = "approach"
                self.last_distance = 0.0
                self.consecutive_precision_count = 0

        # 调试输出
        if hasattr(new_config, 'debug_movement') and new_config.debug_movement:
            print(f"🔄 MovementController配置已更新: 控制方法={self.control_method}, PID参数=({self.kp:.2f},{self.ki:.3f},{self.kd:.3f})")
        
    def _apply_movement_prediction(self, x: float, y: float) -> Tuple[float, float]:
        """Apply movement prediction for smoother tracking, with overshoot prevention."""
        # 如果没有足够的历史数据或没有激活增强功能，则不应用预测
        if len(self.movement_history) < 2 or not hasattr(self, 'enhancement_duration'):
            return x, y

        # 如果增强功能未激活，则不应用预测
        if self.enhancement_duration <= 0:
            return x, y

        # Calculate current distance to target for prediction adjustment
        import math
        current_distance = math.sqrt(x**2 + y**2)

        # Reduce or disable prediction when close to target to prevent overshoot
        if current_distance < 10:  # Close to target
            prediction_factor = 0.02  # Minimal prediction
        elif current_distance < 25:  # Medium distance
            prediction_factor = 0.05  # Reduced prediction
        else:  # Far from target
            prediction_factor = 0.1   # Normal prediction

        # Calculate movement velocity from history
        recent_movements = self.movement_history[-2:]
        velocity_x = recent_movements[-1][0] - recent_movements[-2][0]
        velocity_y = recent_movements[-1][1] - recent_movements[-2][1]

        # Apply distance-adjusted prediction
        predicted_x = x + velocity_x * prediction_factor
        predicted_y = y + velocity_y * prediction_factor

        return predicted_x, predicted_y
        
    def _update_movement_history(self, x: float, y: float) -> None:
        """Update movement history for analysis."""
        self.movement_history.append((x, y))
        
        # Maintain history length
        if len(self.movement_history) > self.max_history_length:
            self.movement_history.pop(0)
            
    def get_movement_statistics(self) -> dict:
        """Get movement statistics for analysis."""
        if not self.movement_history:
            return {}
            
        movements = np.array(self.movement_history)
        
        return {
            'average_movement': np.mean(movements, axis=0).tolist(),
            'movement_variance': np.var(movements, axis=0).tolist(),
            'total_movements': len(self.movement_history),
            'current_offset': 0.0  # offset模式已删除
        }

    # 移除运行时参数更新方法，使用传统配置模式
