"""
    DisplayHelper - Advanced Visual Processing System
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import time
import random
import math
import numpy as np
import gc
import os
import win32api
from typing import Optional, Tuple

from movement_controller import MovementController
from input_manager import get_input_implementation
from unified_visual_processor import UnifiedVisualProcessor
from system_manager import SystemManager
from behavior_randomizer import BehaviorRandomizer

# 绉婚櫎杩愯�屾椂閰嶇疆鐩戞帶鍣�锛屼娇鐢ㄤ紶缁熼厤缃�妯″紡


class DisplayHelper:
    """Advanced visual processing and input management system."""
    
    def __init__(self):
        """Initialize the display helper system."""
        self.is_running = False
        self.system_manager = None
        self.movement_controller = None
        self.input_manager = None
        self.visual_processor = None
        self.behavior_randomizer = BehaviorRandomizer()

        # 寤舵椂鍘嬫灙鐘舵€佽窡韪�
        self.left_click_start_time = 0.0
        self.is_left_clicking = False
        self.enhanced_compensation_active = False

        # 绱�绉�鍋忕Щ閲忚窡韪� (鐢ㄤ簬闄愬埗鏈€澶у亸绉�)
        self.accumulated_offset_x = 0.0
        self.accumulated_offset_y = 0.0
        
        # Performance optimization
        self.last_cleanup_time = time.time()
        self.cleanup_interval = 30.0  # Cleanup every 30 seconds

        # Performance monitoring
        self.frame_times = []
        self.max_frame_history = 100
        self.performance_warning_threshold = 50.0  # ms

        # Error recovery
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        self.error_recovery_delay = 1.0

        # Action trigger stability filtering
        self.trigger_history = []
        self.trigger_stability_frames = 3  # Require 3 consecutive frames
        self.last_action_time = 0
        self.action_cooldown = 0.1  # 100ms cooldown between actions

        # 绉婚櫎杩愯�屾椂閰嶇疆鐩戞帶锛屼娇鐢ㄤ紶缁熼厤缃�妯″紡
        
    def start_processing(self):
        """Start the main processing loop."""
        self._print_system_info()
        
        while True:  # Main loop with configuration hot-reload support
            try:
                # Initialize system components
                if not self._initialize_components():
                    break
                    
                self.is_running = True

                # 绉婚櫎杩愯�屾椂鐩戞帶鍚�鍔�锛屼娇鐢ㄤ紶缁熼厤缃�妯″紡

                # Main processing loop
                self._main_processing_loop()
                
            except Exception as e:
                self._handle_processing_error(e)
                self._cleanup_resources()
                time.sleep(self.error_recovery_delay)
                
    def _initialize_components(self) -> bool:
        """Initialize all system components."""
        try:
            # Record initialization start time
            start_time = time.time()
            
            self.system_manager = SystemManager()
            config = self.system_manager.get_configuration()
            
            self.movement_controller = MovementController(config)
            self.input_manager = get_input_implementation(config)
            self.visual_processor = UnifiedVisualProcessor(config)

            # 馃敡 鍚庡潗鍔涙帶鍒跺凡绠€鍖栦负鐩存帴琛ュ伩妯″紡

            return True
            
        except Exception as e:
            return False
            
    def _main_processing_loop(self):
        """Main processing loop for visual analysis and input handling."""
        frame_start_time = time.time()
        
        while self.is_running:
            try:
                # Calculate frame timing
                current_time = time.time()
                delta_time = current_time - frame_start_time
                frame_start_time = current_time
                
                # Check for configuration reload
                if self.system_manager.check_system_controls():
                    # Try hot reload first, fallback to full restart if needed
                    if not self._try_hot_reload_config():
                        break  # Exit loop to reload configuration
                
                # Process visual input and handle responses
                self._process_frame(delta_time)

                # Record frame processing time
                frame_time_ms = (time.time() - frame_start_time) * 1000
                self._record_frame_time(frame_time_ms)

                # Reset error count on successful frame
                self._reset_error_count()

                # Periodic cleanup for performance
                self._periodic_cleanup(current_time)

                # 绉婚櫎蹇冭烦鍙戦€侊紝浣跨敤浼犵粺閰嶇疆妯″紡

                # Frame rate control
                self._control_frame_rate(frame_start_time)
                
            except Exception as e:
                time.sleep(0.001)  # Brief pause to prevent tight error loop
                
        # Cleanup when exiting loop
        self._cleanup_resources()

        # 绉婚櫎杩愯�屾椂鐩戞帶鍋滄��锛屼娇鐢ㄤ紶缁熼厤缃�妯″紡

        pass  # System restart required
        
    def _process_frame(self, delta_time: float):
        """Process a single frame of visual input."""
        config = self.system_manager.get_configuration()
        
        # Get continuous enhancement state for autonomous control
        enhancement_active, enhancement_duration = self.system_manager.get_continuous_enhancement_state()

        # Determine if processing is needed
        processing_needed = (
            enhancement_active or
            self.system_manager.get_auto_action_state() or
            (config.debug and config.debug_always_on)
        )
        
        if processing_needed:
            # Get visual target information
            target_info, action_trigger = self.visual_processor.analyze_frame(
                self.movement_controller.get_offset_compensation()
            )
            
            # Handle automatic actions with stability filtering
            if self.system_manager.get_auto_action_state() and action_trigger:
                # Add stability filtering to prevent false triggers
                if self._is_action_trigger_stable(action_trigger):
                    self._handle_automatic_action(config)
            
            # Calculate movement adjustments with autonomous control
            self.movement_controller.calculate_movement(
                enhancement_active,
                target_info
            )

            # 馃敡 淇�澶嶏細鑾峰彇骞跺彂閫佽�＄畻鍑虹殑绉诲姩閲�
            if hasattr(self.movement_controller, 'movement_x') and hasattr(self.movement_controller, 'movement_y'):
                move_x = int(self.movement_controller.movement_x)
                move_y = int(self.movement_controller.movement_y)

                # 鍙戦€佺Щ鍔ㄥ懡浠ゅ埌input_manager
                if move_x != 0 or move_y != 0:
                    self.input_manager.send_cursor_movement(move_x, move_y)

            # Pass enhancement duration to movement controller for autonomous control
            if hasattr(self.movement_controller, 'set_enhancement_duration'):
                self.movement_controller.set_enhancement_duration(enhancement_duration)
        else:
            # 濡傛灉涓嶉渶瑕佸�勭悊锛岀‘淇濈Щ鍔ㄦ帶鍒跺櫒鐘舵€佽��閲嶇疆
            self.movement_controller.calculate_movement(False, None)
        
        # Handle rapid input mode
        if self.system_manager.get_rapid_input_state():
            self.input_manager.perform_action()
        
        # 馃敡 澧炲己鐨勫悗鍧愬姏鎺у埗锛欶3+宸﹂敭寤舵椂鍘嬫灙
        if self.system_manager.get_compensation_state():
            current_time = time.time()
            left_key_pressed = win32api.GetAsyncKeyState(0x01) < 0

            # 璺熻釜宸﹂敭鎸変笅鐘舵€�
            if left_key_pressed and not self.is_left_clicking:
                # 宸﹂敭鍒氭寜涓�
                self.left_click_start_time = current_time
                self.is_left_clicking = True
                self.enhanced_compensation_active = False
                # 閲嶇疆绱�绉�鍋忕Щ閲�
                self.accumulated_offset_x = 0.0
                self.accumulated_offset_y = 0.0

            elif not left_key_pressed and self.is_left_clicking:
                # 宸﹂敭閲婃斁
                self.is_left_clicking = False
                self.enhanced_compensation_active = False
                # 寮€濮嬫仮澶嶅亸绉婚噺
                self._start_offset_recovery()

            # 濡傛灉宸﹂敭鎸佺画鎸変笅锛岃�＄畻鍘嬫灙
            if left_key_pressed and self.is_left_clicking:
                # 鑾峰彇閰嶇疆
                config = self.system_manager.get_configuration()

                # 鑾峰彇寤舵椂閰嶇疆
                delay_ms = getattr(config, 'delay_before_compensation', 0)
                enhanced_multiplier = getattr(config, 'enhanced_compensation_multiplier', 2.5)
                light_factor = getattr(config, 'light_compensation_factor', 0.3)
                max_offset = getattr(config, 'max_offset', 500)

                # 璁＄畻鎸変笅鏃堕棿
                hold_duration_ms = (current_time - self.left_click_start_time) * 1000

                # 鍩虹�€琛ュ伩鍊�
                base_comp_x = getattr(config, 'compensation_x', -5.0) * delta_time
                base_comp_y = getattr(config, 'compensation_y', 200.0) * delta_time

                # 鍒ゆ柇鏄�鍚﹁繘鍏ュ�炲己鍘嬫灙妯″紡
                if hold_duration_ms >= delay_ms:
                    if not self.enhanced_compensation_active:
                        self.enhanced_compensation_active = True

                    # 澧炲己鍘嬫灙锛氫娇鐢ㄥ€嶆暟
                    comp_x = base_comp_x * enhanced_multiplier
                    comp_y = base_comp_y * enhanced_multiplier

                    # 娣诲姞绱�绉�鏁堝簲锛氭椂闂磋秺闀匡紝鍘嬫灙瓒婂己
                    time_factor = min(2.0, 1.0 + (hold_duration_ms - delay_ms) / 1000.0)
                    comp_x *= time_factor
                    comp_y *= time_factor

                else:
                    # 寤舵椂鏈熼棿锛氫娇鐢ㄥ彲璋冭交寰�琛ュ伩绯绘暟
                    comp_x = base_comp_x * light_factor  # 鍙�璋冨己搴�
                    comp_y = base_comp_y * light_factor

                # 馃敡 淇�澶嶏細濡傛灉杞诲井琛ュ伩绯绘暟涓�0锛屽畬鍏ㄥ仠姝㈣ˉ鍋�
                if light_factor == 0.0 and hold_duration_ms < delay_ms:
                    comp_x = 0.0
                    comp_y = 0.0
                else:
                    # 娣诲姞浜烘€у寲闅忔満鍙樺寲锛堜粎鍦ㄦ湁琛ュ伩鏃讹級
                    if comp_x != 0:
                        comp_x += np.random.normal(0, abs(comp_x) * 0.08)
                    if comp_y != 0:
                        comp_y += np.random.normal(0, abs(comp_y) * 0.08)

                # 馃敡 妫€鏌ユ槸鍚﹂渶瑕佸彂閫佺Щ鍔ㄥ懡浠�
                if abs(comp_x) > 0.01 or abs(comp_y) > 0.01:  # 鍙�鏈夊湪鏈夊疄闄呰ˉ鍋挎椂鎵嶅�勭悊
                    # 妫€鏌ユ渶澶у亸绉婚噺闄愬埗
                    future_offset_x = self.accumulated_offset_x + comp_x
                    future_offset_y = self.accumulated_offset_y + comp_y

                    # 璁＄畻鎬诲亸绉昏窛绂�
                    total_offset = math.sqrt(future_offset_x**2 + future_offset_y**2)

                    # 濡傛灉瓒呰繃鏈€澶у亸绉婚噺锛屽仠姝㈢Щ鍔�
                    if total_offset > max_offset:
                        pass  # 闈欓粯杈惧埌闄愬埗
                    else:
                        # 鍙戦€佽ˉ鍋跨Щ鍔�
                        self.input_manager.send_cursor_movement(int(comp_x), int(comp_y))

                        # 鏇存柊绱�绉�鍋忕Щ閲�
                        self.accumulated_offset_x += comp_x
                        self.accumulated_offset_y += comp_y

                        # 闈欓粯杩愯�岋紝鏃犺皟璇曡緭鍑�
                else:
                    # 闈欓粯杩愯�岋紝鏃犺皟璇曡緭鍑�
                    pass
        
        # Execute calculated movements with final hotkey check
        movement_x, movement_y = self.movement_controller.get_movement_delta()
        if movement_x != 0 or movement_y != 0:
            # 鍦ㄦ墽琛岀Щ鍔ㄥ墠鏈€鍚庝竴娆℃�€鏌ョ儹閿�鐘舵€�
            current_enhancement_state = self.system_manager.get_enhancement_state()
            if current_enhancement_state:
                self.input_manager.move_cursor(movement_x, movement_y)
            else:
                # 鐑�閿�宸叉澗寮€锛岀珛鍗冲仠姝㈠苟娓呴浂绉诲姩
                self.movement_controller.emergency_stop()
                if hasattr(self.input_manager, 'emergency_stop'):
                    self.input_manager.emergency_stop()

        # Reset movement deltas
        self.movement_controller.reset_movement_delta()
        
    def _handle_automatic_action(self, config):
        """Handle automatic action with human-like timing."""
        if config.action_delay != 0:
            # Calculate action delay with randomization
            delay = self.behavior_randomizer.get_action_delay(
                config.action_delay, 
                config.action_randomization
            )
        else:
            delay = 0
            
        self.input_manager.perform_action(delay)
        
    def _control_frame_rate(self, frame_start_time: float):
        """Control the frame rate to maintain consistent timing."""
        config = self.system_manager.get_configuration()
        frame_time = (time.time() - frame_start_time) * 1000
        
        if frame_time < config.min_frame_time:
            sleep_time = (config.min_frame_time - frame_time) / 1000
            time.sleep(sleep_time)
            
    def _periodic_cleanup(self, current_time: float):
        """Perform periodic cleanup for memory management."""
        if current_time - self.last_cleanup_time > self.cleanup_interval:
            gc.collect()  # Force garbage collection
            self.last_cleanup_time = current_time
            self._check_performance_health()

    def _check_performance_health(self):
        """Check system performance and log warnings if needed."""
        if len(self.frame_times) > 10:
            avg_frame_time = sum(self.frame_times[-10:]) / 10
            if avg_frame_time > self.performance_warning_threshold:
                pass  # Performance warning threshold exceeded

    def _record_frame_time(self, frame_time_ms: float):
        """Record frame processing time for performance monitoring."""
        self.frame_times.append(frame_time_ms)
        if len(self.frame_times) > self.max_frame_history:
            self.frame_times.pop(0)

    def _handle_processing_error(self, error: Exception):
        """Handle processing errors with recovery logic."""
        self.consecutive_errors += 1

        if self.consecutive_errors >= self.max_consecutive_errors:
            self.error_recovery_delay = min(5.0, self.error_recovery_delay * 1.5)

        # Reset error count on successful operation (called elsewhere)

    def _reset_error_count(self):
        """Reset error count after successful operation."""
        if self.consecutive_errors > 0:
            self.consecutive_errors = 0
            self.error_recovery_delay = 1.0
            
    def _cleanup_resources(self):
        """Clean up system resources."""
        try:
            if hasattr(self, 'system_manager') and self.system_manager:
                del self.system_manager
            if hasattr(self, 'movement_controller') and self.movement_controller:
                del self.movement_controller
            if hasattr(self, 'input_manager') and self.input_manager:
                del self.input_manager
            if hasattr(self, 'visual_processor') and self.visual_processor:
                del self.visual_processor
                
            # Force garbage collection
            gc.collect()
            
        except Exception as e:
            pass  # Cleanup error ignored

    def _try_hot_reload_config(self) -> bool:
        """Try to hot-reload configuration without recreating components.

        Returns:
            bool: True if hot reload was successful, False if full restart is needed
        """
        try:
            # Reload configuration in system manager
            self.system_manager.settings_manager.reload_settings()
            self.system_manager._load_key_bindings()

            # Detect what changed
            has_hot_reload_changes, has_restart_changes = self.system_manager.detect_config_changes()

            if has_restart_changes:
                return False

            if not has_hot_reload_changes:
                return True

            # Get updated configuration
            new_config = self.system_manager.get_configuration()

            # Update all components
            if self.movement_controller:
                self.movement_controller.update_config(new_config)

            if self.input_manager and hasattr(self.input_manager, 'update_config'):
                self.input_manager.update_config(new_config)

            if self.visual_processor:
                self.visual_processor.update_config(new_config)

            return True

        except Exception as e:
            return False

    def _start_offset_recovery(self):
        """寮€濮嬪亸绉婚噺鎭㈠�嶈繃绋�"""
        # 鑾峰彇閰嶇疆
        config = self.system_manager.get_configuration()
        recovery_rate = getattr(config, 'recovery_rate', 0.85)

        # 閫愭笎鎭㈠�嶅亸绉婚噺
        if abs(self.accumulated_offset_x) > 0.1 or abs(self.accumulated_offset_y) > 0.1:
            # 璁＄畻鎭㈠�嶇Щ鍔�
            recovery_x = -self.accumulated_offset_x * recovery_rate * 0.1  # 姣忓抚鎭㈠��10%
            recovery_y = -self.accumulated_offset_y * recovery_rate * 0.1

            # 鍙戦€佹仮澶嶇Щ鍔�
            if abs(recovery_x) > 0.1 or abs(recovery_y) > 0.1:
                self.input_manager.send_cursor_movement(int(recovery_x), int(recovery_y))

                # 鏇存柊绱�绉�鍋忕Щ閲�
                self.accumulated_offset_x += recovery_x
                self.accumulated_offset_y += recovery_y

    # 绉婚櫎杩愯�屾椂鐩戞帶璁剧疆鍜屽洖璋冩柟娉曪紝浣跨敤浼犵粺閰嶇疆妯″紡

    def _print_system_info(self):
        """Print system information and license."""
        pass  # System info removed for cleaner output

    def _is_action_trigger_stable(self, action_trigger: bool) -> bool:
        """Check if action trigger is stable across multiple frames to prevent false triggers."""
        import time

        current_time = time.time()

        # Check cooldown period
        if current_time - self.last_action_time < self.action_cooldown:
            return False

        # Add current trigger state to history
        self.trigger_history.append(action_trigger)

        # Keep only recent history
        if len(self.trigger_history) > self.trigger_stability_frames:
            self.trigger_history.pop(0)

        # Require all recent frames to have trigger=True
        if len(self.trigger_history) >= self.trigger_stability_frames:
            all_triggered = all(self.trigger_history)
            if all_triggered:
                self.last_action_time = current_time
                return True

        return False
