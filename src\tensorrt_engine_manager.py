#!/usr/bin/env python3
"""
TensorRT引擎管理器
基于TYOLO的TensorRT引擎优化技术
"""
import sys
import time
import numpy as np
from pathlib import Path

class TensorRTEngineManager:
    """TensorRT引擎管理器"""
    
    def __init__(self, config):
        self.config = config
        self.engine = None
        self.context = None
        self.stream = None
        self.bindings = []
        self.input_shape = None
        self.output_shape = None
        
        # 性能统计
        self.inference_times = []
        self.total_inferences = 0
        
    def load_engine(self, engine_path: str):
        """加载TensorRT引擎"""
        print(f"🔥 加载TensorRT引擎: {engine_path}")
        
        try:
            import tensorrt as trt
            try:
                import pycuda.driver as cuda
                import pycuda.autoinit
                PYCUDA_AVAILABLE = True
            except ImportError:
                print("⚠️ PyCUDA未安装，使用TensorRT内置CUDA支持")
                PYCUDA_AVAILABLE = False
            
            # 创建TensorRT logger
            TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
            
            # 加载引擎文件
            with open(engine_path, 'rb') as f:
                engine_data = f.read()
            
            # 反序列化引擎
            runtime = trt.Runtime(TRT_LOGGER)
            self.engine = runtime.deserialize_cuda_engine(engine_data)
            
            if self.engine is None:
                raise RuntimeError("TensorRT引擎加载失败")
            
            # 创建执行上下文
            self.context = self.engine.create_execution_context()
            
            # 创建CUDA流
            self.stream = cuda.Stream()
            
            # 设置绑定
            self._setup_bindings()
            
            print(f"✅ TensorRT引擎加载成功")
            print(f"   输入形状: {self.input_shape}")
            print(f"   输出形状: {self.output_shape}")
            
            return True
            
        except ImportError:
            print("❌ TensorRT未安装，无法加载引擎")
            return False
        except Exception as e:
            print(f"❌ TensorRT引擎加载失败: {e}")
            return False
    
    def _setup_bindings(self):
        """设置输入输出绑定"""
        import pycuda.driver as cuda
        
        self.bindings = []
        self.inputs = []
        self.outputs = []
        
        for i in range(self.engine.num_bindings):
            binding_name = self.engine.get_binding_name(i)
            binding_shape = self.engine.get_binding_shape(i)
            binding_dtype = self.engine.get_binding_dtype(i)
            
            # 计算内存大小
            size = abs(np.prod(binding_shape))
            
            if self.engine.binding_is_input(i):
                # 输入绑定
                self.input_shape = binding_shape
                host_mem = cuda.pagelocked_empty(size, dtype=np.float32)
                device_mem = cuda.mem_alloc(host_mem.nbytes)
                self.inputs.append({'host': host_mem, 'device': device_mem})
                print(f"   输入绑定 {i}: {binding_name}, 形状: {binding_shape}")
            else:
                # 输出绑定
                self.output_shape = binding_shape
                host_mem = cuda.pagelocked_empty(size, dtype=np.float32)
                device_mem = cuda.mem_alloc(host_mem.nbytes)
                self.outputs.append({'host': host_mem, 'device': device_mem})
                print(f"   输出绑定 {i}: {binding_name}, 形状: {binding_shape}")
            
            self.bindings.append(int(device_mem))
    
    def convert_onnx_to_engine(self, onnx_path: str, engine_path: str):
        """将ONNX模型转换为TensorRT引擎 - 兼容新版TensorRT"""
        print(f"🔄 转换ONNX模型为TensorRT引擎")
        print(f"   输入: {onnx_path}")
        print(f"   输出: {engine_path}")

        try:
            import tensorrt as trt
            print(f"✅ TensorRT版本: {trt.__version__}")

            # 创建builder和config
            TRT_LOGGER = trt.Logger(trt.Logger.INFO)
            builder = trt.Builder(TRT_LOGGER)
            config = builder.create_builder_config()

            # RTX 3060专用配置 - 兼容新版TensorRT API
            workspace_size = 2 << 30  # 2GB工作空间
            try:
                # TensorRT 8.0+ 新API
                config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, workspace_size)
                print("✅ 使用新版TensorRT API设置工作空间: 2GB")
            except (AttributeError, TypeError):
                try:
                    # TensorRT 7.x 旧API回退
                    config.max_workspace_size = workspace_size
                    print("✅ 使用旧版TensorRT API设置工作空间: 2GB")
                except AttributeError:
                    print("⚠️ 无法设置工作空间大小，使用默认值")

            # 启用FP16精度
            if builder.platform_has_fast_fp16:
                config.set_flag(trt.BuilderFlag.FP16)
                print("✅ 启用FP16精度优化")
            else:
                print("⚠️ GPU不支持FP16，使用FP32精度")

            # 启用其他优化标志 (兼容性检查)
            try:
                config.set_flag(trt.BuilderFlag.STRICT_TYPES)
                print("✅ 启用严格类型检查")
            except AttributeError:
                print("⚠️ 当前TensorRT版本不支持STRICT_TYPES标志")

            # 创建网络
            network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            parser = trt.OnnxParser(network, TRT_LOGGER)

            # 解析ONNX模型
            print("📖 解析ONNX模型...")
            with open(onnx_path, 'rb') as model:
                if not parser.parse(model.read()):
                    print("❌ ONNX模型解析失败:")
                    for error in range(parser.num_errors):
                        print(f"   错误 {error}: {parser.get_error(error)}")
                    return False

            print("✅ ONNX模型解析成功")

            # 设置输入形状（如果需要）
            input_tensor = network.get_input(0)
            print(f"📋 输入张量信息: {input_tensor.name}, 形状: {input_tensor.shape}")

            if input_tensor.shape[0] == -1:
                # 动态批处理，设置为固定大小
                print("🔧 设置动态形状优化配置...")
                profile = builder.create_optimization_profile()
                profile.set_shape(input_tensor.name, (1, 3, 640, 640), (1, 3, 640, 640), (4, 3, 640, 640))
                config.add_optimization_profile(profile)
                print("✅ 动态形状配置完成")

            print("🏗️ 构建TensorRT引擎...")
            print("   这可能需要几分钟时间，请耐心等待...")
            start_time = time.time()

            # 构建引擎 - 兼容新版TensorRT API
            try:
                # TensorRT 10.0+ 新API
                serialized_engine = builder.build_serialized_network(network, config)
                if serialized_engine is None:
                    print("❌ TensorRT引擎序列化失败")
                    return False

                # 反序列化引擎以验证
                runtime = trt.Runtime(TRT_LOGGER)
                engine = runtime.deserialize_cuda_engine(serialized_engine)

            except AttributeError:
                # TensorRT 8.x 旧API回退
                engine = builder.build_engine(network, config)
                if engine is None:
                    print("❌ TensorRT引擎构建失败")
                    return False
                serialized_engine = engine.serialize()

            if engine is None:
                print("❌ TensorRT引擎构建失败")
                return False

            build_time = time.time() - start_time
            print(f"✅ TensorRT引擎构建完成，耗时: {build_time:.1f}s")

            # 保存引擎
            print("💾 保存TensorRT引擎...")
            with open(engine_path, 'wb') as f:
                f.write(serialized_engine)

            print(f"✅ TensorRT引擎已保存: {engine_path}")

            # 显示优化信息
            engine_size = Path(engine_path).stat().st_size / (1024*1024)
            onnx_size = Path(onnx_path).stat().st_size / (1024*1024)

            print(f"\n📊 转换结果:")
            print(f"   ONNX大小: {onnx_size:.1f}MB")
            print(f"   引擎大小: {engine_size:.1f}MB")
            print(f"   大小变化: {((engine_size - onnx_size) / onnx_size * 100):+.1f}%")
            print(f"   预期性能提升: 2-5倍")

            return True

        except ImportError as e:
            print(f"❌ TensorRT导入失败: {e}")
            print("请确保已正确安装TensorRT")
            return False
        except Exception as e:
            print(f"❌ 引擎转换失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def inference(self, input_data: np.ndarray):
        """执行TensorRT推理"""
        if self.engine is None or self.context is None:
            raise RuntimeError("TensorRT引擎未加载")
        
        try:
            import pycuda.driver as cuda
            
            start_time = time.time()
            
            # 预处理输入数据
            input_data = self._preprocess_input(input_data)
            
            # 复制输入数据到GPU
            np.copyto(self.inputs[0]['host'], input_data.ravel())
            cuda.memcpy_htod_async(self.inputs[0]['device'], self.inputs[0]['host'], self.stream)
            
            # 执行推理
            self.context.execute_async_v2(bindings=self.bindings, stream_handle=self.stream.handle)
            
            # 复制输出数据到CPU
            cuda.memcpy_dtoh_async(self.outputs[0]['host'], self.outputs[0]['device'], self.stream)
            
            # 同步流
            self.stream.synchronize()
            
            # 后处理输出
            output = self._postprocess_output(self.outputs[0]['host'])
            
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.total_inferences += 1
            
            # 保持最近100次推理时间
            if len(self.inference_times) > 100:
                self.inference_times.pop(0)
            
            return output
            
        except Exception as e:
            print(f"❌ TensorRT推理失败: {e}")
            raise
    
    def _preprocess_input(self, input_data: np.ndarray) -> np.ndarray:
        """预处理输入数据"""
        # 确保输入形状正确
        if len(input_data.shape) == 3:
            input_data = np.expand_dims(input_data, axis=0)
        
        # 转换为float32
        if input_data.dtype != np.float32:
            input_data = input_data.astype(np.float32)
        
        # 归一化到[0,1]
        if input_data.max() > 1.0:
            input_data = input_data / 255.0
        
        return input_data
    
    def _postprocess_output(self, output_data: np.ndarray) -> np.ndarray:
        """后处理输出数据"""
        # 重塑输出形状
        output = output_data.reshape(self.output_shape)
        return output
    
    def get_performance_stats(self):
        """获取性能统计"""
        if not self.inference_times:
            return {}
        
        avg_time = sum(self.inference_times) / len(self.inference_times)
        min_time = min(self.inference_times)
        max_time = max(self.inference_times)
        
        return {
            'total_inferences': self.total_inferences,
            'avg_inference_time': avg_time,
            'min_inference_time': min_time,
            'max_inference_time': max_time,
            'avg_fps': 1.0 / avg_time if avg_time > 0 else 0,
            'recent_samples': len(self.inference_times)
        }
    
    def cleanup(self):
        """清理资源"""
        if self.context:
            del self.context
        if self.engine:
            del self.engine
        if self.stream:
            self.stream.synchronize()
            del self.stream
        
        print("✅ TensorRT资源已清理")

def create_tensorrt_engine_from_onnx(onnx_path: str, engine_path: str, config=None):
    """从ONNX创建TensorRT引擎的便捷函数"""
    manager = TensorRTEngineManager(config)
    
    if manager.convert_onnx_to_engine(onnx_path, engine_path):
        print(f"🎉 TensorRT引擎创建成功: {engine_path}")
        return True
    else:
        print(f"❌ TensorRT引擎创建失败")
        return False

def test_tensorrt_performance(engine_path: str, config=None):
    """测试TensorRT引擎性能"""
    print(f"🧪 测试TensorRT引擎性能: {engine_path}")
    
    manager = TensorRTEngineManager(config)
    
    if not manager.load_engine(engine_path):
        return False
    
    # 创建测试输入
    test_input = np.random.rand(1, 3, 640, 640).astype(np.float32)
    
    # 预热
    print("🔥 引擎预热中...")
    for _ in range(10):
        manager.inference(test_input)
    
    # 性能测试
    print("🔄 执行性能测试...")
    test_runs = 100
    
    for i in range(test_runs):
        manager.inference(test_input)
        if (i + 1) % 20 == 0:
            print(f"   已完成 {i + 1}/{test_runs} 次推理")
    
    # 显示结果
    stats = manager.get_performance_stats()
    print(f"\n📊 TensorRT性能测试结果:")
    print(f"   总推理次数: {stats['total_inferences']}")
    print(f"   平均推理时间: {stats['avg_inference_time']*1000:.1f}ms")
    print(f"   最快推理时间: {stats['min_inference_time']*1000:.1f}ms")
    print(f"   最慢推理时间: {stats['max_inference_time']*1000:.1f}ms")
    print(f"   平均FPS: {stats['avg_fps']:.1f}")
    
    manager.cleanup()
    return True

if __name__ == "__main__":
    # 测试TensorRT引擎管理器
    onnx_path = "models/CS2.onnx"  # 使用标准ONNX模型而非量化版本
    engine_path = "models/CS2_tensorrt.engine"
    
    if Path(onnx_path).exists():
        # 创建引擎
        create_tensorrt_engine_from_onnx(onnx_path, engine_path)
        
        # 测试性能
        if Path(engine_path).exists():
            test_tensorrt_performance(engine_path)
    else:
        print(f"❌ ONNX模型文件不存在: {onnx_path}")
