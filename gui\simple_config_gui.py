"""
    Enhanced Config GUI - 完整配置管理界面
    支持所有配置选项、性能优化和实时监控
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import configparser
from pathlib import Path
import glob
import shutil
import os
import sys
import threading
import time
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

# 尝试导入性能监控工具
try:
    from utils.performance_utils import PerformanceMonitor, UnifiedExceptionHandler
    PERFORMANCE_UTILS_AVAILABLE = True
except ImportError:
    PERFORMANCE_UTILS_AVAILABLE = False

class SimpleConfigGUI:
    """增强的配置GUI，支持完整配置管理和性能监控."""

    def __init__(self):
        """Initialize the enhanced GUI."""
        print("🔧 初始化增强配置GUI...")

        # 基本配置
        self.project_root = Path(__file__).parent.parent
        self.config_file = self.project_root / "settings.ini"
        self.backup_file = self.project_root / "settings_backup.ini"
        self.config = configparser.ConfigParser()

        # GUI状态
        self.is_hidden = False
        self.root = None
        self.control_panel = None
        self.notebook = None

        # 性能监控
        self.performance_monitor = None
        self.monitoring_active = False
        self.stats_update_thread = None

        # 配置变量 - 基础设置
        self.detection_mode_var = None
        self.input_method_var = None
        self.movement_speed_var = None
        self.smoothing_factor_var = None

        # 配置变量 - YOLO设置
        self.model_type_var = None
        self.model_path_var = None
        self.model_format_var = None
        self.tensorrt_enabled_var = None
        self.confidence_threshold_var = None
        self.target_classes_vars = {}  # 目标类别选择变量
        self.nms_threshold_var = None
        self.use_gpu_var = None
        self.batch_size_var = None

        # 配置变量 - 性能设置
        self.max_fps_var = None
        self.cache_size_var = None
        self.cache_ttl_var = None
        self.model_cache_size_var = None

        # 配置变量 - 颜色检测
        self.color_lower_var = None
        self.color_upper_var = None

        # 性能统计显示变量
        self.fps_display_var = None
        self.cache_hit_rate_var = None
        self.memory_usage_var = None
        self.error_count_var = None

        # 配置文件管理
        self.current_config_name = "settings.ini"
        self.config_profiles_dir = self.project_root / "config_profiles"

        # 预设配置模板
        self.config_presets = {
            "高性能": {
                "max_fps": 120,
                "cache_size": 100,
                "cache_ttl": 0.05,
                "model_cache_size": 5,
                "detection_history_length": 5,
                "movement_history_length": 10
            },
            "平衡": {
                "max_fps": 60,
                "cache_size": 50,
                "cache_ttl": 0.1,
                "model_cache_size": 3,
                "detection_history_length": 3,
                "movement_history_length": 5
            },
            "省资源": {
                "max_fps": 30,
                "cache_size": 20,
                "cache_ttl": 0.2,
                "model_cache_size": 1,
                "detection_history_length": 2,
                "movement_history_length": 3
            }
        }
        self.config_profiles_dir.mkdir(exist_ok=True)
        self.available_configs = []
        self.config_list_var = None

        # 移除运行时管理器，使用传统配置模式

        # 加载配置
        self.load_config()

        # 扫描可用模型
        self.available_models = self.scan_available_models()

        # 扫描可用配置文件
        self.scan_config_profiles()

        # 热重载状态管理
        self.init_hotreload_config()
        
    def load_config(self):
        """Load configuration from file."""
        try:
            if self.config_file.exists():
                self.config.read(str(self.config_file), encoding='utf-8')
                print(f"✅ 配置加载成功: {len(self.config.sections())} sections")
            else:
                print(f"❌ 配置文件不存在: {self.config_file}")
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")

    def init_hotreload_config(self):
        """初始化热重载配置管理."""
        # 定义需要重启的参数 (基于测试结果)
        self.restart_required_params = {
            # 核心模式参数
            ('detection', 'detection_mode'),
            ('visual_yolo', 'model_type'),
            ('visual_yolo', 'model_format'),
            ('input', 'input_method'),
            ('capture', 'capture_region_x'),
            ('capture', 'capture_region_y'),
            # 模型路径相关
            ('visual_yolo', 'model_path'),
            ('visual_yolo', 'use_tensorrt'),
        }

        # 支持热重载的参数 (基于测试结果)
        self.hotreload_supported_params = {
            # 自瞄控制 - 修复配置节匹配
            ('enhancement', 'movement_speed'),  # 修复：使用enhancement节
            ('movement', 'movement_smoothing_factor'),
            ('movement', 'vertical_speed_multiplier'),
            ('movement', 'control_method'),
            ('movement', 'pid_kp'),
            ('movement', 'pid_ki'),
            ('movement', 'pid_kd'),
            ('movement', 'debug_pid'),
            # 目标定位
            ('enhancement', 'target_height_ratio'),
            # 检测精度
            ('visual_yolo', 'confidence_threshold'),
            ('visual_yolo', 'nms_threshold'),
            ('visual_yolo', 'target_classes'),
            # 扳机功能
            ('action', 'action_delay'),
            ('action', 'action_randomization'),
            ('action', 'action_threshold'),
            # 后坐力补偿
            ('compensation', 'mode'),
            ('compensation', 'compensation_x'),
            ('compensation', 'compensation_y'),
            ('compensation', 'recovery_rate'),
            # 延时压枪配置
            ('compensation', 'delay_before_compensation'),
            ('compensation', 'enhanced_compensation_multiplier'),
            ('compensation', 'light_compensation_factor'),
            # 性能优化
            ('performance', 'max_fps'),
            ('performance', 'cache_size'),
            ('performance', 'cache_ttl'),
            # 其他可热重载参数
            ('visual_yolo', 'batch_size'),
            ('visual_yolo', 'use_gpu'),
        }

        # 控件状态管理
        self.widget_states = {}
        self.restart_required_widgets = []
        self.hotreload_widgets = []

    def scan_available_models(self):
        """扫描可用的YOLO模型文件."""
        print("🔍 扫描可用模型文件...")

        models = []
        model_extensions = ['*.onnx', '*.pt', '*.engine', '*.trt']

        # 扫描项目根目录和常见模型目录
        search_dirs = [
            self.project_root,
            self.project_root / "models",
            self.project_root / "weights",
            self.project_root / "yolo",
            self.project_root / "src" / "models"
        ]

        for search_dir in search_dirs:
            if search_dir.exists():
                for ext in model_extensions:
                    pattern = str(search_dir / ext)
                    found_files = glob.glob(pattern)
                    for file_path in found_files:
                        model_name = Path(file_path).name
                        if model_name not in models:
                            models.append(model_name)

        # 如果没有找到模型文件，添加默认选项
        if not models:
            models = ["yolov5n", "yolov5s", "yolov8n", "yolov8s", "yolo11n", "yolo11s"]
            print("⚠️ 未找到模型文件，使用默认模型列表")
        else:
            print(f"✅ 找到 {len(models)} 个模型文件: {models}")

        return sorted(models)

    def validate_model_file(self, model_path):
        """验证模型文件."""
        if not model_path:
            return False, "未选择模型文件"

        model_file = Path(model_path)
        if not model_file.exists():
            return False, f"模型文件不存在: {model_path}"

        # 检查文件扩展名
        valid_extensions = ['.onnx', '.pt', '.engine', '.trt']
        if model_file.suffix.lower() not in valid_extensions:
            return False, f"不支持的模型格式: {model_file.suffix}"

        # 检查文件大小
        file_size = model_file.stat().st_size
        if file_size == 0:
            return False, "模型文件为空"

        # 检查TensorRT引擎
        if model_file.suffix.lower() in ['.engine', '.trt']:
            return True, f"TensorRT引擎 ({file_size / (1024*1024):.1f}MB)"

        # 检查ONNX模型
        if model_file.suffix.lower() == '.onnx':
            try:
                import onnx
                model = onnx.load(str(model_file))
                input_shape = model.graph.input[0].type.tensor_type.shape.dim
                if len(input_shape) >= 4:
                    h = input_shape[2].dim_value
                    w = input_shape[3].dim_value
                    return True, f"ONNX模型 ({h}×{w}, {file_size / (1024*1024):.1f}MB)"
                else:
                    return True, f"ONNX模型 ({file_size / (1024*1024):.1f}MB)"
            except Exception as e:
                return False, f"ONNX模型验证失败: {e}"

        # 检查PyTorch模型
        if model_file.suffix.lower() == '.pt':
            return True, f"PyTorch模型 ({file_size / (1024*1024):.1f}MB)"

        return True, f"模型文件 ({file_size / (1024*1024):.1f}MB)"

    def get_model_status_color(self, is_valid):
        """获取模型状态颜色."""
        return "green" if is_valid else "red"

    def register_widget_for_hotreload_management(self, widget, section, key, widget_type="entry"):
        """注册控件用于热重载状态管理."""
        param_key = (section, key)

        # 确定控件是否需要重启
        requires_restart = param_key in self.restart_required_params

        # 存储控件信息
        widget_info = {
            'widget': widget,
            'section': section,
            'key': key,
            'type': widget_type,
            'requires_restart': requires_restart,
            'original_state': widget.cget('state') if hasattr(widget, 'cget') else 'normal'
        }

        self.widget_states[widget] = widget_info

        if requires_restart:
            self.restart_required_widgets.append(widget)
        else:
            self.hotreload_widgets.append(widget)

        return requires_restart

    def apply_widget_states_after_startup(self):
        """在主程序启动后应用控件状态 - 只有在主程序实际运行时才调用."""
        # 检查主程序是否真的在运行
        if not self.is_main_program_running():
            print("🔧 主程序未运行，跳过控件状态管理")
            return

        print("🔧 应用动态控件状态管理...")

        restart_count = 0
        hotreload_count = 0

        for widget, info in self.widget_states.items():
            try:
                if info['requires_restart']:
                    # 设置需要重启的控件为禁用状态
                    if hasattr(widget, 'configure'):
                        widget.configure(state='disabled')
                    restart_count += 1
                else:
                    # 保持热重载控件为可用状态
                    if hasattr(widget, 'configure'):
                        widget.configure(state=info['original_state'])
                    hotreload_count += 1
            except Exception as e:
                print(f"⚠️ 设置控件状态失败: {e}")

        print(f"✅ 控件状态管理完成: {restart_count}个需重启, {hotreload_count}个可热重载")

    def unlock_all_widgets(self):
        """解锁所有控件，恢复为可编辑状态."""
        print("🔓 解锁所有控件...")

        unlocked_count = 0

        for widget, info in self.widget_states.items():
            try:
                if hasattr(widget, 'configure'):
                    widget.configure(state=info['original_state'])
                    unlocked_count += 1
            except Exception as e:
                print(f"⚠️ 解锁控件失败: {e}")

        print(f"✅ 已解锁 {unlocked_count} 个控件")

    def is_main_program_running(self):
        """检查主程序是否正在运行."""
        import psutil
        import os

        try:
            # 检查是否有start.py进程在运行
            current_pid = os.getpid()
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['pid'] == current_pid:
                        continue  # 跳过当前GUI进程

                    cmdline = proc.info['cmdline']
                    if cmdline and any('start.py' in arg for arg in cmdline):
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return False
        except Exception as e:
            print(f"⚠️ 检查主程序状态失败: {e}")
            # 如果检查失败，默认认为主程序未运行，保持控件可用
            return False

    def create_tooltip(self, widget, text):
        """为控件创建工具提示."""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def add_hotreload_indicator(self, parent, section, key):
        """添加热重载状态指示器."""
        param_key = (section, key)
        requires_restart = param_key in self.restart_required_params

        if requires_restart:
            indicator = tk.Label(parent, text="🔄", foreground="orange", font=("Arial", 12))
            tooltip_text = "此参数需要重启程序才能生效"
        else:
            indicator = tk.Label(parent, text="⚡", foreground="green", font=("Arial", 12))
            tooltip_text = "此参数支持F1热重载，立即生效"

        self.create_tooltip(indicator, tooltip_text)
        return indicator

    def scan_config_profiles(self):
        """扫描可用的配置文件."""
        print("🔍 扫描配置文件...")

        self.available_configs = []

        # 扫描配置文件目录
        if self.config_profiles_dir.exists():
            for config_file in self.config_profiles_dir.glob("*.ini"):
                self.available_configs.append(config_file.name)

        # 添加默认配置
        if "settings.ini" not in self.available_configs:
            self.available_configs.insert(0, "settings.ini")

        # 添加一些预设配置模板
        preset_configs = [
            "valorant_config.ini",
            "cs2_config.ini",
            "apex_config.ini",
            "fortnite_config.ini",
            "pubg_config.ini"
        ]

        for preset in preset_configs:
            if preset not in self.available_configs:
                self.available_configs.append(f"[模板] {preset}")

        print(f"✅ 找到 {len([c for c in self.available_configs if not c.startswith('[模板]')])} 个配置文件")
        return self.available_configs

    def save_config(self):
        """Save configuration to file."""
        try:
            with open(str(self.config_file), 'w', encoding='utf-8') as f:
                self.config.write(f)
            print("✅ 配置已保存")
            self.status_var.set("配置已保存")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
            
    def create_gui(self):
        """Create the enhanced main GUI window."""
        print("🖥️ 创建增强GUI窗口...")

        self.root = tk.Tk()
        self.root.title("Unibot 增强配置工具 v2.0")
        self.root.geometry("1200x700")  # 调整窗口大小，宽度增加，高度减少
        self.root.resizable(True, True)

        # 设置最小窗口大小
        self.root.minsize(800, 500)

        # 设置窗口样式
        try:
            self.root.configure(bg='#f0f0f0')
        except:
            pass

        # 绑定快捷键
        self.root.bind("<Escape>", self.toggle_window_visibility)
        self.root.bind("<F12>", self.toggle_window_visibility)
        self.root.bind("<Control-h>", self.toggle_window_visibility)
        self.root.bind("<F1>", self.reload_config)
        self.root.bind("<F5>", self.refresh_performance_stats)

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.hide_window)

        # 初始化性能监控
        self.init_performance_monitoring()

        print("✅ 增强GUI窗口创建完成")

    def init_performance_monitoring(self):
        """初始化性能监控."""
        try:
            if PERFORMANCE_UTILS_AVAILABLE:
                self.performance_monitor = PerformanceMonitor()
                print("✅ 性能监控已初始化")
            else:
                print("⚠️ 性能监控工具不可用")
        except Exception as e:
            print(f"⚠️ 性能监控初始化失败: {e}")

    def toggle_window_visibility(self, event=None):
        """切换窗口显示/隐藏状态."""
        if self.is_hidden:
            self.show_window()
        else:
            self.hide_window()

    def hide_window(self):
        """隐藏主窗口并显示控制面板."""
        if self.root and not self.is_hidden:
            # 隐藏主窗口
            self.root.withdraw()
            self.is_hidden = True

            # 显示小型控制面板
            self.show_control_panel()

            print("🔒 主窗口已隐藏，显示控制面板")
            print("💡 提示: 点击控制面板的'显示主界面'按钮恢复")

    def show_window(self):
        """显示主窗口并隐藏控制面板."""
        if self.root and self.is_hidden:
            # 隐藏控制面板
            self.hide_control_panel()

            # 显示主窗口
            self.root.deiconify()
            self.root.lift()
            self.root.focus_force()
            # 确保窗口在最前面
            self.root.attributes('-topmost', True)
            self.root.after(100, lambda: self.root.attributes('-topmost', False))
            self.is_hidden = False
            print("🖥️ 主窗口已显示")

    def show_control_panel(self):
        """显示小型控制面板."""
        if self.control_panel is None:
            self.control_panel = tk.Toplevel()
            self.control_panel.title("Windows 系统配置")
            self.control_panel.geometry("300x150")
            self.control_panel.resizable(False, False)

            # 设置窗口属性
            self.control_panel.attributes('-topmost', True)

            # 创建控制面板内容
            main_frame = ttk.Frame(self.control_panel)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(main_frame, text="配置界面已隐藏", font=("Arial", 12, "bold"))
            title_label.pack(pady=5)

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=10)

            # 显示主界面按钮
            show_btn = ttk.Button(button_frame, text="显示主界面", command=self.show_window)
            show_btn.pack(side=tk.LEFT, padx=5)

            # 退出按钮
            quit_btn = ttk.Button(button_frame, text="退出程序", command=self.quit_app)
            quit_btn.pack(side=tk.LEFT, padx=5)

            # 提示信息
            tip_label = ttk.Label(main_frame, text="点击'显示主界面'恢复配置窗口",
                                font=("Arial", 9), foreground="gray")
            tip_label.pack(pady=5)

            # 绑定关闭事件
            self.control_panel.protocol("WM_DELETE_WINDOW", self.quit_app)
        else:
            # 如果已存在，显示并置顶
            self.control_panel.deiconify()
            self.control_panel.lift()
            self.control_panel.focus_force()

    def hide_control_panel(self):
        """隐藏控制面板."""
        if self.control_panel:
            self.control_panel.withdraw()

    def quit_app(self):
        """退出应用程序."""
        print("🔄 正在退出应用...")
        try:
            if self.control_panel:
                self.control_panel.destroy()
            if self.root:
                self.root.quit()
                self.root.destroy()
        except:
            pass
        print("✅ 应用已退出")
        
    def create_main_interface(self):
        """创建主界面."""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题和快捷键提示
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=5)

        title_label = ttk.Label(header_frame, text="Unibot 配置工具", font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)

        shortcut_label = ttk.Label(header_frame, text="快捷键: ESC/F12/Ctrl+H 隐藏 | 隐藏后显示控制面板",
                                 font=("Arial", 9), foreground="gray")
        shortcut_label.pack(side=tk.RIGHT)
        
        # 创建标签页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 基本设置标签页
        self.create_basic_tab(notebook)
        
        # 高级设置标签页
        self.create_advanced_tab(notebook)

        # 控制设置标签页
        self.create_control_tab(notebook)

        # 配置管理标签页
        self.create_config_management_tab(notebook)

        # 控制按钮
        self.create_control_buttons(main_frame)

    def create_scrollable_frame(self, parent, tab_name):
        """创建可滚动的框架."""
        # 创建主容器框架
        container = ttk.Frame(parent)
        parent.add(container, text=tab_name)

        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        # 创建canvas窗口
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 配置canvas滚动
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        # 当鼠标进入/离开canvas时绑定/解绑滚轮事件
        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        # 配置canvas窗口大小自适应
        def _configure_canvas(event):
            # 更新scrollable_frame的宽度以匹配canvas
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)

        canvas.bind('<Configure>', _configure_canvas)

        return scrollable_frame

    def create_basic_tab(self, notebook):
        """创建基本设置标签页."""
        # 创建滚动框架
        frame = self.create_scrollable_frame(notebook, "基本设置")
        
        # 检测模式
        ttk.Label(frame, text="检测模式:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.detection_var = tk.StringVar(value=self.config.get('detection', 'detection_mode', fallback='color'))
        detection_combo = ttk.Combobox(frame, textvariable=self.detection_var,
                                     values=["color", "yolo", "hybrid", "auto"], state="readonly")
        detection_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        detection_combo.bind('<<ComboboxSelected>>',
                           lambda e: self.update_config('detection', 'detection_mode', self.detection_var.get()))
        
        # 输入方法
        ttk.Label(frame, text="输入方法:").grid(row=1, column=0, sticky="w", padx=5, pady=5)

        # 创建输入方法选择框架
        input_frame = ttk.Frame(frame)
        input_frame.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        input_frame.columnconfigure(0, weight=1)

        # 尝试从新的[input]节读取，回退到[enhancement]节
        input_value = self.config.get('input', 'input_method', fallback=None)
        if input_value is None:
            input_value = self.config.get('enhancement', 'input_method', fallback='winapi')
        self.input_var = tk.StringVar(value=input_value)

        # 更新的输入方法选项（添加增强ARD系统）
        input_methods = [
            ("enhanced_ard", "增强ARD系统 (🚀双设备协同/基础控制)"),
            ("kmboxnet", "KmBoxNet网络硬件 (🏆反作弊最强)"),
            ("microcontroller_serial", "微控制器串口 (🏆最安全)"),
            ("microcontroller_socket", "微控制器网络 (🏆最安全)"),
            ("thirdparty", "第三方库组合 (🥇推荐)"),
            ("interception", "Interception驱动 (需安装)"),
            ("sendinput", "SendInput API (⚠️可能被阻止)"),
            ("winapi", "Win32 API (⚠️可能被阻止)")
        ]

        # 创建下拉框，只显示值不显示描述
        input_values = [method[0] for method in input_methods]
        input_combo = ttk.Combobox(input_frame, textvariable=self.input_var,
                                 values=input_values, state="readonly", width=15)
        input_combo.grid(row=0, column=0, sticky="w", padx=(0, 5))
        input_combo.bind('<<ComboboxSelected>>',
                        lambda e: [self.update_config_input_method(self.input_var.get()),
                                  self.update_hardware_config_visibility(),
                                  self.update_input_method_info()])

        # 添加输入方法说明标签
        self.input_info_var = tk.StringVar()
        self.input_info_label = ttk.Label(input_frame, textvariable=self.input_info_var,
                                         foreground="blue", font=("Arial", 8))
        self.input_info_label.grid(row=1, column=0, sticky="w", padx=(0, 5))

        # 初始化显示当前方法的说明
        self.update_input_method_info()
        
        # 调试开关
        self.debug_var = tk.BooleanVar(value=self.config.getboolean('debug', 'enabled', fallback=True))
        debug_check = ttk.Checkbutton(frame, text="启用调试", variable=self.debug_var,
                                    command=lambda: self.update_config('debug', 'enabled', str(self.debug_var.get()).lower()))
        debug_check.grid(row=2, column=0, columnspan=2, sticky="w", padx=5, pady=5)
        
        # 移动速度 - 改进的滑块控件
        ttk.Label(frame, text="移动速度:").grid(row=3, column=0, sticky="w", padx=5, pady=5)

        # 创建速度控制框架
        speed_frame = ttk.Frame(frame)
        speed_frame.grid(row=3, column=1, sticky="ew", padx=5, pady=5)
        speed_frame.columnconfigure(0, weight=1)

        # 速度滑块
        self.speed_var = tk.DoubleVar(value=float(self.config.get('enhancement', 'movement_speed', fallback='1.0')))
        speed_scale = ttk.Scale(speed_frame, from_=0.1, to=3.0, variable=self.speed_var,
                               orient=tk.HORIZONTAL, length=200)
        speed_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        # 数值显示和输入
        self.speed_display_var = tk.StringVar(value=f"{self.speed_var.get():.2f}")
        speed_entry = ttk.Entry(speed_frame, textvariable=self.speed_display_var, width=8)
        speed_entry.grid(row=0, column=1, padx=(0, 5))

        # 绑定事件
        speed_scale.bind('<Motion>', lambda e: self.update_speed_display())
        speed_scale.bind('<ButtonRelease-1>', lambda e: self.save_speed_config())
        speed_entry.bind('<Return>', lambda e: self.update_speed_from_entry())
        speed_entry.bind('<FocusOut>', lambda e: self.update_speed_from_entry())

        # 目标高度比例
        ttk.Label(frame, text="目标高度比例:").grid(row=4, column=0, sticky="w", padx=5, pady=5)

        # 创建高度比例控制框架
        height_frame = ttk.Frame(frame)
        height_frame.grid(row=4, column=1, sticky="ew", padx=5, pady=5)
        height_frame.columnconfigure(0, weight=1)

        # 高度比例滑块
        self.height_ratio_var = tk.DoubleVar(value=float(self.config.get('enhancement', 'target_height_ratio', fallback='0.5')))
        height_scale = ttk.Scale(height_frame, from_=0.1, to=1.0, variable=self.height_ratio_var,
                               orient=tk.HORIZONTAL, length=200)
        height_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        # 数值显示和输入
        self.height_ratio_display_var = tk.StringVar(value=f"{self.height_ratio_var.get():.2f}")
        height_entry = ttk.Entry(height_frame, textvariable=self.height_ratio_display_var, width=8)
        height_entry.grid(row=0, column=1, padx=(0, 5))

        # 绑定事件
        height_scale.bind('<Motion>', lambda e: self.update_height_ratio_display())
        height_scale.bind('<ButtonRelease-1>', lambda e: self.save_height_ratio_config())
        height_entry.bind('<Return>', lambda e: self.update_height_ratio_from_entry())
        height_entry.bind('<FocusOut>', lambda e: self.update_height_ratio_from_entry())

        # 移动平滑因子
        ttk.Label(frame, text="移动平滑因子:").grid(row=5, column=0, sticky="w", padx=5, pady=5)

        smoothing_frame = ttk.Frame(frame)
        smoothing_frame.grid(row=5, column=1, sticky="ew", padx=5, pady=5)
        smoothing_frame.columnconfigure(0, weight=1)

        # 平滑因子滑块
        self.smoothing_var = tk.DoubleVar(value=float(self.config.get('movement', 'movement_smoothing_factor', fallback='0.3')))
        smoothing_scale = ttk.Scale(smoothing_frame, from_=0.1, to=0.8, variable=self.smoothing_var,
                                   orient=tk.HORIZONTAL, length=200)
        smoothing_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        # 数值显示和输入
        self.smoothing_display_var = tk.StringVar(value=f"{self.smoothing_var.get():.2f}")
        smoothing_entry = ttk.Entry(smoothing_frame, textvariable=self.smoothing_display_var, width=8)
        smoothing_entry.grid(row=0, column=1, padx=(0, 5))

        # 绑定事件
        smoothing_scale.bind('<Motion>', lambda e: self.update_smoothing_display())
        smoothing_scale.bind('<ButtonRelease-1>', lambda e: self.save_smoothing_config())
        smoothing_entry.bind('<Return>', lambda e: self.update_smoothing_from_entry())
        smoothing_entry.bind('<FocusOut>', lambda e: self.update_smoothing_from_entry())

        # 控制方法选择
        ttk.Label(frame, text="控制方法:").grid(row=6, column=0, sticky="w", padx=5, pady=5)

        control_frame = ttk.Frame(frame)
        control_frame.grid(row=6, column=1, sticky="ew", padx=5, pady=5)

        self.control_method_var = tk.StringVar(value=self.config.get('movement', 'control_method', fallback='streamlined'))
        control_combo = ttk.Combobox(control_frame, textvariable=self.control_method_var,
                                   values=['streamlined', 'pid'], state='readonly', width=15)
        control_combo.grid(row=0, column=0, sticky="w")
        control_combo.bind('<<ComboboxSelected>>', lambda e: self.save_control_method())

        # 控制方法说明
        method_descriptions = {
            'adaptive': '自适应控制 (原始算法)',
            'pid': '传统PID控制 (经典算法)',
            'advanced': '高级智能控制 (推荐)',
            'streamlined': '流线型控制 (极速)'
        }

        self.method_desc_var = tk.StringVar()
        method_desc_label = ttk.Label(control_frame, textvariable=self.method_desc_var,
                                     font=("Arial", 8), foreground="gray")
        method_desc_label.grid(row=0, column=1, sticky="w", padx=(10, 0))

        # 更新说明文字
        def update_method_description(*args):
            current_method = self.control_method_var.get()
            desc = method_descriptions.get(current_method, '')
            self.method_desc_var.set(desc)

        self.control_method_var.trace('w', update_method_description)
        update_method_description()  # 初始化显示

        # PID参数区域（动态显示）
        self.create_pid_config_section(frame)

        # 新增：智能移动算法参数区域
        self.create_enhanced_movement_config_section(frame)

        # 硬件配置区域（动态显示）
        self.create_hardware_config_section(frame)

        frame.columnconfigure(1, weight=1)

    def create_hardware_config_section(self, parent):
        """创建硬件配置区域（动态显示）."""
        # 硬件配置标签
        hardware_label = ttk.Label(parent, text="🔧 硬件配置:", font=("Arial", 10, "bold"))
        hardware_label.grid(row=8, column=0, columnspan=2, sticky="w", padx=10, pady=(20, 5))

        # 硬件配置框架 - 修复布局重叠问题
        self.hardware_config_frame = ttk.LabelFrame(parent, text="", padding="10")
        self.hardware_config_frame.grid(row=9, column=0, columnspan=2, sticky="ew", padx=10, pady=(5, 15))
        self.hardware_config_frame.columnconfigure(1, weight=1)

        # 串口配置
        self.serial_label = ttk.Label(self.hardware_config_frame, text="串口:")
        self.serial_port_var = tk.StringVar(value=self.config.get('device', 'serial_port', fallback='COM1'))
        self.serial_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.serial_port_var, width=15)
        self.serial_entry.bind('<FocusOut>', lambda e: self.update_config('device', 'serial_port', self.serial_port_var.get()))

        # 网络配置
        self.ip_label = ttk.Label(self.hardware_config_frame, text="设备IP:")
        self.device_ip_var = tk.StringVar(value=self.config.get('device', 'device_ip', fallback='0.0.0.0'))
        self.ip_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.device_ip_var, width=15)
        self.ip_entry.bind('<FocusOut>', lambda e: self.update_config('device', 'device_ip', self.device_ip_var.get()))

        self.port_label = ttk.Label(self.hardware_config_frame, text="端口:")
        self.device_port_var = tk.StringVar(value=self.config.get('device', 'device_port', fallback='50256'))
        self.port_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.device_port_var, width=10)
        self.port_entry.bind('<FocusOut>', lambda e: self.update_config('device', 'device_port', self.device_port_var.get()))

        # 初始化显示状态
        self.update_hardware_config_visibility()

    def update_hardware_config_visibility(self):
        """根据输入方法更新硬件配置的显示状态."""
        input_method = self.input_var.get()

        # 清除现有控件
        for widget in self.hardware_config_frame.winfo_children():
            widget.grid_remove()

        if input_method == 'microcontroller_serial':
            # 显示串口配置
            self.serial_label.grid(row=0, column=0, sticky="w", padx=(0, 5), pady=2)
            self.serial_entry.grid(row=0, column=1, sticky="w", padx=(0, 5), pady=2)
        elif input_method == 'microcontroller_socket':
            # 显示网络配置
            self.ip_label.grid(row=0, column=0, sticky="w", padx=(0, 5), pady=2)
            self.ip_entry.grid(row=0, column=1, sticky="w", padx=(0, 5), pady=2)
            self.port_label.grid(row=1, column=0, sticky="w", padx=(0, 5), pady=2)
            self.port_entry.grid(row=1, column=1, sticky="w", padx=(0, 5), pady=2)
        elif input_method == 'thirdparty':
            # 显示第三方库依赖状态
            self.show_thirdparty_status()
        elif input_method == 'kmboxnet':
            # 显示KmBoxNet网络配置
            self.show_kmboxnet_config()
        elif input_method == 'enhanced_ard':
            # 显示增强ARD配置
            self.show_enhanced_ard_config()

    def show_thirdparty_status(self):
        """显示第三方库依赖状态."""
        # 检查第三方库可用性
        dependencies = self.check_thirdparty_dependencies()

        status_label = ttk.Label(self.hardware_config_frame,
                               text="第三方库状态:",
                               font=("Arial", 9, "bold"))
        status_label.grid(row=0, column=0, sticky="w", padx=(0, 5), pady=2)

        row = 1
        for lib_name, available in dependencies.items():
            status_text = f"✅ {lib_name}" if available else f"❌ {lib_name}"
            color = "green" if available else "red"

            lib_label = ttk.Label(self.hardware_config_frame,
                                text=status_text,
                                foreground=color)
            lib_label.grid(row=row, column=0, sticky="w", padx=(10, 5), pady=1)
            row += 1

        # 如果有缺失的库，显示安装提示
        missing_libs = [lib for lib, available in dependencies.items() if not available]
        if missing_libs:
            install_text = f"缺失库: {', '.join(missing_libs)}"
            install_label = ttk.Label(self.hardware_config_frame,
                                    text=install_text,
                                    foreground="orange",
                                    font=("Arial", 8))
            install_label.grid(row=row, column=0, sticky="w", padx=(10, 5), pady=2)

            # 添加安装按钮
            install_btn = ttk.Button(self.hardware_config_frame,
                                   text="安装缺失库",
                                   command=self.install_missing_dependencies)
            install_btn.grid(row=row+1, column=0, sticky="w", padx=(10, 5), pady=2)

    def check_thirdparty_dependencies(self):
        """检查第三方库依赖."""
        dependencies = {}

        # 检查pynput
        try:
            import pynput
            dependencies['pynput'] = True
        except ImportError:
            dependencies['pynput'] = False

        # 检查pyautogui
        try:
            import pyautogui
            dependencies['pyautogui'] = True
        except ImportError:
            dependencies['pyautogui'] = False

        # 检查mouse
        try:
            import mouse
            dependencies['mouse'] = True
        except ImportError:
            dependencies['mouse'] = False

        return dependencies

    def install_missing_dependencies(self):
        """安装缺失的依赖库."""
        import subprocess
        import sys

        missing_libs = []
        dependencies = self.check_thirdparty_dependencies()

        for lib_name, available in dependencies.items():
            if not available:
                missing_libs.append(lib_name)

        if missing_libs:
            try:
                print(f"🔄 正在安装缺失的库: {', '.join(missing_libs)}")
                for lib in missing_libs:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", lib])
                print("✅ 依赖库安装完成")

                # 刷新状态显示
                self.update_hardware_config_visibility()

            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {e}")
                import tkinter.messagebox as msgbox
                msgbox.showerror("安装失败", f"无法安装依赖库: {e}")
        else:
            print("✅ 所有依赖库都已安装")

    def show_kmboxnet_config(self):
        """显示KmBoxNet网络配置."""
        # 清空现有配置
        for widget in self.hardware_config_frame.winfo_children():
            widget.destroy()

        # KmBoxNet配置标题
        title_label = ttk.Label(self.hardware_config_frame,
                               text="🌐 KmBoxNet网络硬件配置",
                               font=("Arial", 10, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, sticky="w", padx=(0, 5), pady=(5, 10))

        # IP地址配置
        ip_label = ttk.Label(self.hardware_config_frame, text="设备IP地址:")
        ip_label.grid(row=1, column=0, sticky="w", padx=(10, 5), pady=3)

        # 从配置中读取当前IP，使用正确的默认值
        current_ip = self.config.get('kmboxnet', 'host', fallback='*************')
        self.kmboxnet_ip_var = tk.StringVar(value=current_ip)
        ip_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.kmboxnet_ip_var, width=18)
        ip_entry.grid(row=1, column=1, sticky="w", padx=(0, 5), pady=3)
        ip_entry.bind('<FocusOut>', self.save_kmboxnet_config)
        ip_entry.bind('<Return>', self.save_kmboxnet_config)

        # IP说明
        ip_tip = ttk.Label(self.hardware_config_frame, text="(默认: *************)",
                          font=("Arial", 8), foreground="gray")
        ip_tip.grid(row=1, column=2, sticky="w", padx=(5, 0), pady=3)

        # 端口配置
        port_label = ttk.Label(self.hardware_config_frame, text="通信端口:")
        port_label.grid(row=2, column=0, sticky="w", padx=(10, 5), pady=3)

        # 从配置中读取当前端口，使用正确的默认值
        current_port = self.config.get('kmboxnet', 'port', fallback='8868')
        self.kmboxnet_port_var = tk.StringVar(value=current_port)
        port_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.kmboxnet_port_var, width=10)
        port_entry.grid(row=2, column=1, sticky="w", padx=(0, 5), pady=3)
        port_entry.bind('<FocusOut>', self.save_kmboxnet_config)
        port_entry.bind('<Return>', self.save_kmboxnet_config)

        # 端口说明
        port_tip = ttk.Label(self.hardware_config_frame, text="(默认: 8868)",
                            font=("Arial", 8), foreground="gray")
        port_tip.grid(row=2, column=2, sticky="w", padx=(5, 0), pady=3)

        # MAC地址配置
        mac_label = ttk.Label(self.hardware_config_frame, text="MAC地址:")
        mac_label.grid(row=3, column=0, sticky="w", padx=(10, 5), pady=3)

        # 从配置中读取当前MAC地址
        current_mac = self.config.get('kmboxnet', 'mac', fallback='63E4E04E')
        self.kmboxnet_mac_var = tk.StringVar(value=current_mac)
        mac_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.kmboxnet_mac_var, width=12)
        mac_entry.grid(row=3, column=1, sticky="w", padx=(0, 5), pady=3)
        mac_entry.bind('<FocusOut>', self.save_kmboxnet_config)
        mac_entry.bind('<Return>', self.save_kmboxnet_config)

        # MAC说明
        mac_tip = ttk.Label(self.hardware_config_frame, text="(默认: 63E4E04E)",
                           font=("Arial", 8), foreground="gray")
        mac_tip.grid(row=3, column=2, sticky="w", padx=(5, 0), pady=3)

        # 按钮框架
        button_frame = ttk.Frame(self.hardware_config_frame)
        button_frame.grid(row=4, column=0, columnspan=3, sticky="ew", padx=(10, 5), pady=(10, 5))

        # 连接测试按钮
        test_btn = ttk.Button(button_frame, text="🔗 测试连接", command=self.test_kmboxnet_connection)
        test_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 重置默认按钮
        reset_btn = ttk.Button(button_frame, text="🔄 重置默认", command=self.reset_kmboxnet_config)
        reset_btn.pack(side=tk.LEFT)

        # 状态显示
        self.kmboxnet_status_var = tk.StringVar(value="未连接")
        status_label = ttk.Label(self.hardware_config_frame,
                               textvariable=self.kmboxnet_status_var,
                               font=("Arial", 9),
                               foreground="orange")
        status_label.grid(row=5, column=0, columnspan=3, sticky="w", padx=(10, 5), pady=(5, 10))

        # 配置列权重
        self.hardware_config_frame.columnconfigure(1, weight=1)

    def save_kmboxnet_config(self, event=None):
        """保存KmBoxNet配置."""
        try:
            # 确保[kmboxnet]节存在
            if not self.config.has_section('kmboxnet'):
                self.config.add_section('kmboxnet')

            # 验证IP地址格式
            ip = self.kmboxnet_ip_var.get().strip()
            if not self._validate_ip_address(ip):
                self.kmboxnet_status_var.set("❌ IP地址格式错误")
                return

            # 验证端口范围
            try:
                port = int(self.kmboxnet_port_var.get().strip())
                if not (1 <= port <= 65535):
                    self.kmboxnet_status_var.set("❌ 端口范围错误(1-65535)")
                    return
            except ValueError:
                self.kmboxnet_status_var.set("❌ 端口必须是数字")
                return

            # 验证MAC地址格式
            mac = self.kmboxnet_mac_var.get().strip().upper()
            if not self._validate_mac_address(mac):
                self.kmboxnet_status_var.set("❌ MAC地址格式错误")
                return

            # 保存配置
            self.config.set('kmboxnet', 'host', ip)
            self.config.set('kmboxnet', 'port', str(port))
            self.config.set('kmboxnet', 'mac', mac)

            # 保存配置文件
            self.save_config()
            self.kmboxnet_status_var.set("✅ 配置已保存")
            print(f"✅ KmBoxNet配置已保存: {ip}:{port} (MAC: {mac})")

        except Exception as e:
            self.kmboxnet_status_var.set(f"❌ 保存失败: {str(e)}")
            print(f"❌ 保存KmBoxNet配置失败: {e}")

    def reset_kmboxnet_config(self):
        """重置KmBoxNet配置为默认值."""
        try:
            # 设置默认值
            self.kmboxnet_ip_var.set('*************')
            self.kmboxnet_port_var.set('8868')
            self.kmboxnet_mac_var.set('63E4E04E')

            # 保存配置
            self.save_kmboxnet_config()
            self.kmboxnet_status_var.set("✅ 已重置为默认配置")
            print("✅ KmBoxNet配置已重置为默认值")

        except Exception as e:
            self.kmboxnet_status_var.set(f"❌ 重置失败: {str(e)}")
            print(f"❌ 重置KmBoxNet配置失败: {e}")

    def show_enhanced_ard_config(self):
        """显示增强ARD配置."""
        # 清空现有配置
        for widget in self.hardware_config_frame.winfo_children():
            widget.destroy()

        # 增强ARD配置标题
        title_label = ttk.Label(self.hardware_config_frame,
                               text="🚀 增强ARD系统配置",
                               font=("Arial", 10, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, sticky="w", padx=(0, 5), pady=(5, 10))

        # 硬件类型选择
        type_label = ttk.Label(self.hardware_config_frame, text="硬件类型:")
        type_label.grid(row=1, column=0, sticky="w", padx=(10, 5), pady=3)

        # 从配置中读取当前硬件类型
        current_type = self.config.get('enhanced_ard', 'hardware_type', fallback='simple_ard')
        self.ard_type_var = tk.StringVar(value=current_type)

        type_options = [
            ("stealth_ard", "🛡️ 隐蔽ARD控制 (反检测优化)"),
            ("simple_ard", "⚡ 基础ARD控制"),
            ("leonardo_usb_host", "🎯 Leonardo + USB Host Shield")
        ]

        type_combo = ttk.Combobox(self.hardware_config_frame, textvariable=self.ard_type_var,
                                 values=[opt[0] for opt in type_options], state="readonly", width=20)
        type_combo.grid(row=1, column=1, sticky="w", padx=(0, 5), pady=3)
        type_combo.bind('<<ComboboxSelected>>', self._on_hardware_type_changed)

        # 类型说明
        type_tip = ttk.Label(self.hardware_config_frame, text="(选择你的硬件配置)",
                            font=("Arial", 8), foreground="gray")
        type_tip.grid(row=1, column=2, sticky="w", padx=(5, 0), pady=3)

        # 串口配置
        port_label = ttk.Label(self.hardware_config_frame, text="串口端口:")
        port_label.grid(row=2, column=0, sticky="w", padx=(10, 5), pady=3)

        # 从配置中读取当前端口
        current_port = self.config.get('enhanced_ard', 'ard_serial_port', fallback='COM11')
        self.ard_port_var = tk.StringVar(value=current_port)
        port_entry = ttk.Entry(self.hardware_config_frame, textvariable=self.ard_port_var, width=10)
        port_entry.grid(row=2, column=1, sticky="w", padx=(0, 5), pady=3)
        port_entry.bind('<FocusOut>', self.save_enhanced_ard_config)
        port_entry.bind('<Return>', self.save_enhanced_ard_config)

        # 端口说明
        port_tip = ttk.Label(self.hardware_config_frame, text="(如: COM11)",
                            font=("Arial", 8), foreground="gray")
        port_tip.grid(row=2, column=2, sticky="w", padx=(5, 0), pady=3)

        # 波特率配置
        baud_label = ttk.Label(self.hardware_config_frame, text="波特率:")
        baud_label.grid(row=3, column=0, sticky="w", padx=(10, 5), pady=3)

        # 从配置中读取当前波特率
        current_baud = self.config.get('enhanced_ard', 'ard_baud_rate', fallback='115200')
        self.ard_baud_var = tk.StringVar(value=current_baud)
        baud_combo = ttk.Combobox(self.hardware_config_frame, textvariable=self.ard_baud_var,
                                 values=['9600', '57600', '115200', '230400'], state="readonly", width=10)
        baud_combo.grid(row=3, column=1, sticky="w", padx=(0, 5), pady=3)
        baud_combo.bind('<<ComboboxSelected>>', self.save_enhanced_ard_config)

        # 波特率说明
        baud_tip = ttk.Label(self.hardware_config_frame, text="(推荐: 115200)",
                            font=("Arial", 8), foreground="gray")
        baud_tip.grid(row=3, column=2, sticky="w", padx=(5, 0), pady=3)

        # 隐蔽模式配置（仅在stealth_ard模式下显示）
        self.stealth_frame = None
        self._update_stealth_config_visibility()

        # 按钮框架
        button_frame = ttk.Frame(self.hardware_config_frame)
        button_frame.grid(row=8, column=0, columnspan=3, sticky="ew", padx=(10, 5), pady=(10, 5))

        # 连接测试按钮
        test_btn = ttk.Button(button_frame, text="🔗 测试连接", command=self.test_enhanced_ard_connection)
        test_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 重置默认按钮
        reset_btn = ttk.Button(button_frame, text="🔄 重置默认", command=self.reset_enhanced_ard_config)
        reset_btn.pack(side=tk.LEFT)

        # 状态显示
        self.ard_status_var = tk.StringVar(value="未连接")
        status_label = ttk.Label(self.hardware_config_frame,
                               textvariable=self.ard_status_var,
                               font=("Arial", 9),
                               foreground="orange")
        status_label.grid(row=9, column=0, columnspan=3, sticky="w", padx=(10, 5), pady=(5, 10))

        # 配置列权重
        self.hardware_config_frame.columnconfigure(1, weight=1)

    def save_enhanced_ard_config(self, event=None):
        """保存增强ARD配置."""
        try:
            # 确保[enhanced_ard]节存在
            if not self.config.has_section('enhanced_ard'):
                self.config.add_section('enhanced_ard')

            # 保存配置
            self.config.set('enhanced_ard', 'hardware_type', self.ard_type_var.get())
            self.config.set('enhanced_ard', 'ard_serial_port', self.ard_port_var.get())
            self.config.set('enhanced_ard', 'ard_baud_rate', self.ard_baud_var.get())

            # 保存隐蔽模式配置（如果存在）
            if hasattr(self, 'stealth_profile_var'):
                self.config.set('enhanced_ard', 'stealth_profile', self.stealth_profile_var.get())
            if hasattr(self, 'behavior_pattern_var'):
                self.config.set('enhanced_ard', 'behavior_pattern', self.behavior_pattern_var.get())
            if hasattr(self, 'stealth_level_var'):
                self.config.set('enhanced_ard', 'stealth_level', self.stealth_level_var.get())
            if hasattr(self, 'silent_mode_var'):
                self.config.set('enhanced_ard', 'silent_mode', str(self.silent_mode_var.get()).lower())

            # 保存配置文件
            self.save_config()
            self.ard_status_var.set("✅ 配置已保存")
            print(f"✅ 增强ARD配置已保存: {self.ard_type_var.get()} @ {self.ard_port_var.get()}")

        except Exception as e:
            self.ard_status_var.set(f"❌ 保存失败: {str(e)}")
            print(f"❌ 保存增强ARD配置失败: {e}")

    def test_enhanced_ard_connection(self):
        """测试增强ARD连接."""
        try:
            import serial
            import threading

            # 更新状态为测试中
            self.ard_status_var.set("🔄 正在测试连接...")
            self.root.update()

            # 获取配置
            port = self.ard_port_var.get().strip()
            baud = self.ard_baud_var.get().strip()

            # 验证输入
            if not port or not baud:
                self.ard_status_var.set("❌ 请填写完整配置")
                return

            # 在后台线程中进行连接测试
            def test_connection():
                try:
                    # 尝试连接串口
                    test_serial = serial.Serial(port, int(baud), timeout=2)
                    time.sleep(1)  # 等待Arduino重启

                    # 发送测试命令
                    test_serial.write(b"STATUS\r")
                    test_serial.flush()

                    # 读取响应
                    response = test_serial.readline().decode().strip()
                    test_serial.close()

                    # 更新状态
                    if "STATUS:" in response or "READY" in response:
                        self.ard_status_var.set("✅ 连接成功")
                    else:
                        self.ard_status_var.set("⚠️ 连接成功但固件可能不匹配")

                except serial.SerialException as e:
                    self.ard_status_var.set(f"❌ 串口连接失败: {str(e)}")
                except Exception as e:
                    self.ard_status_var.set(f"❌ 测试失败: {str(e)}")

            # 启动测试线程
            test_thread = threading.Thread(target=test_connection, daemon=True)
            test_thread.start()

        except Exception as e:
            self.ard_status_var.set(f"❌ 测试错误: {str(e)}")
            print(f"❌ 增强ARD连接测试失败: {e}")

    def reset_enhanced_ard_config(self):
        """重置增强ARD配置为默认值."""
        try:
            # 设置默认值
            self.ard_type_var.set('stealth_ard')  # 默认使用隐蔽模式
            self.ard_port_var.set('COM11')
            self.ard_baud_var.set('115200')

            # 重置隐蔽配置
            if hasattr(self, 'stealth_profile_var'):
                self.stealth_profile_var.set('logitech_g502')
            if hasattr(self, 'behavior_pattern_var'):
                self.behavior_pattern_var.set('human_like')
            if hasattr(self, 'stealth_level_var'):
                self.stealth_level_var.set('high')
            if hasattr(self, 'silent_mode_var'):
                self.silent_mode_var.set(True)

            # 更新隐蔽配置显示
            self._update_stealth_config_visibility()

            # 保存配置
            self.save_enhanced_ard_config()
            self.ard_status_var.set("✅ 已重置为默认配置")
            print("✅ 增强ARD配置已重置为默认值")

        except Exception as e:
            self.ard_status_var.set(f"❌ 重置失败: {str(e)}")
            print(f"❌ 重置增强ARD配置失败: {e}")

    def _on_hardware_type_changed(self, event=None):
        """硬件类型改变时的处理"""
        self._update_stealth_config_visibility()
        self.save_enhanced_ard_config()

    def _update_stealth_config_visibility(self):
        """更新隐蔽配置的可见性"""
        if not hasattr(self, 'ard_type_var'):
            return

        hardware_type = self.ard_type_var.get()

        # 清除现有的隐蔽配置
        if self.stealth_frame:
            self.stealth_frame.destroy()
            self.stealth_frame = None

        # 如果是隐蔽模式，显示隐蔽配置
        if hardware_type == 'stealth_ard':
            self._create_stealth_config()

    def _create_stealth_config(self):
        """创建隐蔽模式配置界面"""
        # 创建隐蔽配置框架
        self.stealth_frame = ttk.LabelFrame(self.hardware_config_frame, text="🛡️ 隐蔽模式配置", padding=10)
        self.stealth_frame.grid(row=4, column=0, columnspan=3, sticky="ew", padx=(10, 5), pady=(10, 5))

        # 伪装设备选择
        profile_label = ttk.Label(self.stealth_frame, text="伪装设备:")
        profile_label.grid(row=0, column=0, sticky="w", padx=(0, 5), pady=3)

        # 从配置中读取当前伪装设备
        current_profile = self.config.get('enhanced_ard', 'stealth_profile', fallback='logitech_g502')
        self.stealth_profile_var = tk.StringVar(value=current_profile)

        profile_options = [
            ("logitech_g502", "Logitech G502 HERO"),
            ("razer_deathadder", "Razer DeathAdder V3"),
            ("steelseries_rival", "SteelSeries Rival 600"),
            ("corsair_m65", "Corsair M65 RGB Elite"),
            ("generic_office", "通用办公鼠标")
        ]

        profile_combo = ttk.Combobox(self.stealth_frame, textvariable=self.stealth_profile_var,
                                   values=[opt[0] for opt in profile_options], state="readonly", width=20)
        profile_combo.grid(row=0, column=1, sticky="w", padx=(0, 5), pady=3)
        profile_combo.bind('<<ComboboxSelected>>', self.save_enhanced_ard_config)

        # 行为模式选择
        behavior_label = ttk.Label(self.stealth_frame, text="行为模式:")
        behavior_label.grid(row=1, column=0, sticky="w", padx=(0, 5), pady=3)

        current_behavior = self.config.get('enhanced_ard', 'behavior_pattern', fallback='human_like')
        self.behavior_pattern_var = tk.StringVar(value=current_behavior)

        behavior_options = [
            ("human_like", "人性化模拟"),
            ("gaming_optimized", "游戏优化"),
            ("office_casual", "办公休闲")
        ]

        behavior_combo = ttk.Combobox(self.stealth_frame, textvariable=self.behavior_pattern_var,
                                    values=[opt[0] for opt in behavior_options], state="readonly", width=20)
        behavior_combo.grid(row=1, column=1, sticky="w", padx=(0, 5), pady=3)
        behavior_combo.bind('<<ComboboxSelected>>', self.save_enhanced_ard_config)

        # 隐蔽等级选择
        level_label = ttk.Label(self.stealth_frame, text="隐蔽等级:")
        level_label.grid(row=2, column=0, sticky="w", padx=(0, 5), pady=3)

        current_level = self.config.get('enhanced_ard', 'stealth_level', fallback='high')
        self.stealth_level_var = tk.StringVar(value=current_level)

        level_options = [
            ("high", "高 (最大隐蔽)"),
            ("medium", "中 (平衡模式)"),
            ("low", "低 (基础隐蔽)")
        ]

        level_combo = ttk.Combobox(self.stealth_frame, textvariable=self.stealth_level_var,
                                 values=[opt[0] for opt in level_options], state="readonly", width=20)
        level_combo.grid(row=2, column=1, sticky="w", padx=(0, 5), pady=3)
        level_combo.bind('<<ComboboxSelected>>', self.save_enhanced_ard_config)

        # 静默模式开关
        current_silent = self.config.get('enhanced_ard', 'silent_mode', fallback='true').lower() == 'true'
        self.silent_mode_var = tk.BooleanVar(value=current_silent)

        silent_check = ttk.Checkbutton(self.stealth_frame, text="静默模式 (减少日志输出)",
                                     variable=self.silent_mode_var,
                                     command=self.save_enhanced_ard_config)
        silent_check.grid(row=3, column=0, columnspan=2, sticky="w", padx=(0, 5), pady=3)

        # 配置列权重
        self.stealth_frame.columnconfigure(1, weight=1)

    def _validate_ip_address(self, ip):
        """验证IP地址格式."""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not (0 <= int(part) <= 255):
                    return False
            return True
        except:
            return False

    def _validate_mac_address(self, mac):
        """验证MAC地址格式."""
        try:
            # 支持多种MAC地址格式
            mac = mac.upper().strip()

            # 8位十六进制格式 (KmBoxNet常用)
            if len(mac) == 8 and all(c in '0123456789ABCDEF' for c in mac):
                return True

            # 10位十六进制格式
            if len(mac) == 10 and all(c in '0123456789ABCDEF' for c in mac):
                return True

            # 12位十六进制格式 (标准MAC地址)
            if len(mac) == 12 and all(c in '0123456789ABCDEF' for c in mac):
                return True

            # 带分隔符的MAC地址格式 (AA:BB:CC:DD:EE:FF)
            if len(mac) == 17 and mac.count(':') == 5:
                parts = mac.split(':')
                if len(parts) == 6 and all(len(part) == 2 and all(c in '0123456789ABCDEF' for c in part) for part in parts):
                    return True

            # 带分隔符的MAC地址格式 (AA-BB-CC-DD-EE-FF)
            if len(mac) == 17 and mac.count('-') == 5:
                parts = mac.split('-')
                if len(parts) == 6 and all(len(part) == 2 and all(c in '0123456789ABCDEF' for c in part) for part in parts):
                    return True

            return False
        except:
            return False

    def test_kmboxnet_connection(self):
        """测试KmBoxNet连接."""
        try:
            import socket
            import threading

            # 更新状态为测试中
            self.kmboxnet_status_var.set("🔄 正在测试连接...")
            self.root.update()

            # 获取配置
            host = self.kmboxnet_ip_var.get().strip()
            port_str = self.kmboxnet_port_var.get().strip()
            mac = self.kmboxnet_mac_var.get().strip()

            # 验证输入
            if not host or not port_str or not mac:
                self.kmboxnet_status_var.set("❌ 请填写完整配置")
                return

            try:
                port = int(port_str)
            except ValueError:
                self.kmboxnet_status_var.set("❌ 端口必须是数字")
                return

            # 在后台线程中进行连接测试
            def test_connection():
                try:
                    # 创建socket连接测试
                    test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    test_socket.settimeout(5.0)  # 5秒超时

                    # 尝试连接
                    test_socket.connect((host, port))
                    test_socket.close()

                    # 连接成功
                    self.root.after(0, lambda: self.kmboxnet_status_var.set("✅ 网络连接成功"))
                    print(f"✅ KmBoxNet网络连接测试成功: {host}:{port}")

                    # 尝试导入kmNet库进行更深入的测试
                    try:
                        import kmNet
                        result = kmNet.init(host, str(port), mac)
                        if result == 0:
                            self.root.after(0, lambda: self.kmboxnet_status_var.set("✅ KmBoxNet连接完全成功"))
                            print(f"✅ KmBoxNet硬件连接测试成功")
                        else:
                            self.root.after(0, lambda: self.kmboxnet_status_var.set(f"⚠️ 网络连接成功，但硬件初始化失败(代码:{result})"))
                            print(f"⚠️ KmBoxNet硬件初始化失败，错误代码: {result}")
                    except ImportError:
                        self.root.after(0, lambda: self.kmboxnet_status_var.set("✅ 网络连接成功 (未安装kmNet库)"))
                        print("⚠️ kmNet库未安装，仅进行了网络连接测试")
                    except Exception as e:
                        self.root.after(0, lambda: self.kmboxnet_status_var.set(f"⚠️ 网络连接成功，硬件测试失败: {str(e)[:30]}"))
                        print(f"⚠️ KmBoxNet硬件测试失败: {e}")

                except socket.timeout:
                    self.root.after(0, lambda: self.kmboxnet_status_var.set("❌ 连接超时，请检查IP和端口"))
                    print(f"❌ KmBoxNet连接超时: {host}:{port}")
                except ConnectionRefusedError:
                    self.root.after(0, lambda: self.kmboxnet_status_var.set("❌ 连接被拒绝，请检查设备状态"))
                    print(f"❌ KmBoxNet连接被拒绝: {host}:{port}")
                except Exception as e:
                    error_msg = str(e)[:50]
                    self.root.after(0, lambda: self.kmboxnet_status_var.set(f"❌ 连接失败: {error_msg}"))
                    print(f"❌ KmBoxNet连接测试失败: {e}")

            # 启动测试线程
            test_thread = threading.Thread(target=test_connection, daemon=True)
            test_thread.start()

        except Exception as e:
            self.kmboxnet_status_var.set(f"❌ 测试异常: {str(e)[:30]}")
            print(f"❌ KmBoxNet连接测试异常: {e}")

    def create_advanced_tab(self, notebook):
        """创建增强的模型设置标签页."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🤖 模型设置")

        # 创建滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        # 创建各个设置区域
        self.create_model_settings_section(scrollable_frame)
        self.create_detection_params_section(scrollable_frame)

    def create_model_settings_section(self, parent):
        """创建模型设置区域."""
        model_frame = ttk.LabelFrame(parent, text="🤖 模型配置", padding=10)
        model_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        model_frame.columnconfigure(1, weight=1)

        row = 0

        # 模型类型选择
        ttk.Label(model_frame, text="模型文件:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.model_var = tk.StringVar(value=self.config.get('visual_yolo', 'model_type', fallback='CS2.onnx'))
        model_combo = ttk.Combobox(model_frame, textvariable=self.model_var,
                                  values=self.available_models, state="readonly", width=25)
        model_combo.grid(row=row, column=1, sticky="ew", padx=(0, 5), pady=2)
        model_combo.bind('<<ComboboxSelected>>',
                        lambda e: self.on_model_changed())

        # 注册控件并添加状态指示器
        self.register_widget_for_hotreload_management(model_combo, 'visual_yolo', 'model_type', 'combobox')
        indicator = self.add_hotreload_indicator(model_frame, 'visual_yolo', 'model_type')
        indicator.grid(row=row, column=2, padx=5)

        # 刷新模型按钮
        refresh_btn = ttk.Button(model_frame, text="🔄", width=3, command=self.refresh_models)
        refresh_btn.grid(row=row, column=3, padx=5)

        row += 1

        # 模型状态显示
        ttk.Label(model_frame, text="模型状态:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.model_status_var = tk.StringVar(value="检查中...")
        self.model_status_label = ttk.Label(model_frame, textvariable=self.model_status_var,
                                           font=("Arial", 9), foreground="gray")
        self.model_status_label.grid(row=row, column=1, sticky="w", padx=(0, 5), pady=2)

        # 验证模型按钮
        validate_btn = ttk.Button(model_frame, text="验证", width=8, command=self.validate_current_model)
        validate_btn.grid(row=row, column=2, columnspan=2, padx=5)

        row += 1

        # 模型格式设置
        ttk.Label(model_frame, text="模型格式:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.model_format_var = tk.StringVar(value=self.config.get('visual_yolo', 'model_format', fallback='auto'))
        format_combo = ttk.Combobox(model_frame, textvariable=self.model_format_var,
                                   values=['auto', 'onnx', 'pytorch', 'tensorrt'], state="readonly", width=15)
        format_combo.grid(row=row, column=1, sticky="w", padx=(0, 5), pady=2)
        format_combo.bind('<<ComboboxSelected>>',
                         lambda e: self.update_config('visual_yolo', 'model_format', self.model_format_var.get()))

        # 注册控件
        self.register_widget_for_hotreload_management(format_combo, 'visual_yolo', 'model_format', 'combobox')
        indicator = self.add_hotreload_indicator(model_frame, 'visual_yolo', 'model_format')
        indicator.grid(row=row, column=2, padx=5)

        row += 1

        # TensorRT设置
        ttk.Label(model_frame, text="TensorRT加速:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.tensorrt_var = tk.BooleanVar(value=self.config.getboolean('visual_yolo', 'use_tensorrt', fallback=False))
        tensorrt_check = ttk.Checkbutton(model_frame, text="启用TensorRT引擎", variable=self.tensorrt_var,
                                        command=lambda: self.update_config('visual_yolo', 'use_tensorrt',
                                                                          str(self.tensorrt_var.get()).lower()))
        tensorrt_check.grid(row=row, column=1, sticky="w", padx=(0, 5), pady=2)

        # 注册控件
        self.register_widget_for_hotreload_management(tensorrt_check, 'visual_yolo', 'use_tensorrt', 'checkbutton')
        indicator = self.add_hotreload_indicator(model_frame, 'visual_yolo', 'use_tensorrt')
        indicator.grid(row=row, column=2, padx=5)

        row += 1

        # GPU设置
        ttk.Label(model_frame, text="GPU加速:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.use_gpu_var = tk.BooleanVar(value=self.config.getboolean('visual_yolo', 'use_gpu', fallback=True))
        gpu_check = ttk.Checkbutton(model_frame, text="启用GPU加速", variable=self.use_gpu_var,
                                   command=lambda: self.update_config('visual_yolo', 'use_gpu',
                                                                     str(self.use_gpu_var.get()).lower()))
        gpu_check.grid(row=row, column=1, sticky="w", padx=(0, 5), pady=2)

        # 注册控件
        self.register_widget_for_hotreload_management(gpu_check, 'visual_yolo', 'use_gpu', 'checkbutton')
        indicator = self.add_hotreload_indicator(model_frame, 'visual_yolo', 'use_gpu')
        indicator.grid(row=row, column=2, padx=5)

    def create_detection_params_section(self, parent):
        """创建检测参数设置区域."""
        params_frame = ttk.LabelFrame(parent, text="🎯 检测参数", padding=10)
        params_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        params_frame.columnconfigure(1, weight=1)

        row = 0

        # 置信度阈值 - 改进的滑块控件
        ttk.Label(params_frame, text="置信度阈值:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)

        # 创建置信度控制框架
        conf_frame = ttk.Frame(params_frame)
        conf_frame.grid(row=row, column=1, sticky="ew", padx=(0, 5), pady=2)
        conf_frame.columnconfigure(0, weight=1)

        # 置信度滑块
        self.conf_var = tk.DoubleVar(value=float(self.config.get('visual_yolo', 'confidence_threshold', fallback='0.5')))
        conf_scale = ttk.Scale(conf_frame, from_=0.1, to=0.9, variable=self.conf_var,
                              orient=tk.HORIZONTAL, length=200)
        conf_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        # 数值显示和输入
        self.conf_display_var = tk.StringVar(value=f"{self.conf_var.get():.2f}")
        conf_entry = ttk.Entry(conf_frame, textvariable=self.conf_display_var, width=8)
        conf_entry.grid(row=0, column=1, padx=(0, 5))

        # 绑定事件
        conf_scale.bind('<Motion>', lambda e: self.update_conf_display())
        conf_scale.bind('<ButtonRelease-1>', lambda e: self.save_conf_config())
        conf_entry.bind('<Return>', lambda e: self.update_conf_from_entry())
        conf_entry.bind('<FocusOut>', lambda e: self.update_conf_from_entry())

        # 注册控件
        self.register_widget_for_hotreload_management(conf_scale, 'visual_yolo', 'confidence_threshold', 'scale')
        self.register_widget_for_hotreload_management(conf_entry, 'visual_yolo', 'confidence_threshold', 'entry')
        indicator = self.add_hotreload_indicator(params_frame, 'visual_yolo', 'confidence_threshold')
        indicator.grid(row=row, column=2, padx=5)

        row += 1

        # NMS阈值
        ttk.Label(params_frame, text="NMS阈值:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.nms_var = tk.DoubleVar(value=float(self.config.get('visual_yolo', 'nms_threshold', fallback='0.4')))
        nms_scale = ttk.Scale(params_frame, from_=0.1, to=0.9, variable=self.nms_var,
                             orient=tk.HORIZONTAL, length=200)
        nms_scale.grid(row=row, column=1, sticky="ew", padx=(0, 5), pady=2)
        nms_scale.bind('<ButtonRelease-1>',
                      lambda e: self.update_config('visual_yolo', 'nms_threshold', f"{self.nms_var.get():.2f}"))

        # 注册控件
        self.register_widget_for_hotreload_management(nms_scale, 'visual_yolo', 'nms_threshold', 'scale')
        indicator = self.add_hotreload_indicator(params_frame, 'visual_yolo', 'nms_threshold')
        indicator.grid(row=row, column=2, padx=5)

        row += 1

        # 捕获区域
        ttk.Label(params_frame, text="捕获区域大小:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.region_var = tk.IntVar(value=int(self.config.get('capture', 'capture_region_x', fallback='320')))
        region_spin = ttk.Spinbox(params_frame, from_=64, to=1024, textvariable=self.region_var, width=10)
        region_spin.grid(row=row, column=1, sticky="w", padx=(0, 5), pady=2)
        region_spin.bind('<FocusOut>',
                        lambda e: [self.update_config('capture', 'capture_region_x', str(self.region_var.get())),
                                  self.update_config('capture', 'capture_region_y', str(self.region_var.get()))])

        # 注册控件
        self.register_widget_for_hotreload_management(region_spin, 'capture', 'capture_region_x', 'spinbox')
        indicator = self.add_hotreload_indicator(params_frame, 'capture', 'capture_region_x')
        indicator.grid(row=row, column=2, padx=5)

        row += 1

        # 批处理大小
        ttk.Label(params_frame, text="批处理大小:").grid(row=row, column=0, sticky="w", padx=(0, 5), pady=2)
        self.batch_size_var = tk.IntVar(value=int(self.config.get('visual_yolo', 'batch_size', fallback='1')))
        batch_spin = ttk.Spinbox(params_frame, from_=1, to=16, textvariable=self.batch_size_var, width=10)
        batch_spin.grid(row=row, column=1, sticky="w", padx=(0, 5), pady=2)
        batch_spin.bind('<FocusOut>',
                       lambda e: self.update_config('visual_yolo', 'batch_size', str(self.batch_size_var.get())))

        # 注册控件
        self.register_widget_for_hotreload_management(batch_spin, 'visual_yolo', 'batch_size', 'spinbox')
        indicator = self.add_hotreload_indicator(params_frame, 'visual_yolo', 'batch_size')
        indicator.grid(row=row, column=2, padx=5)

        row += 1

        # 目标类别选择
        self.create_target_classes_section(params_frame, row)

    def create_target_classes_section(self, parent, start_row):
        """创建目标类别选择区域."""
        # 目标类别标签
        ttk.Label(parent, text="目标类别:").grid(row=start_row, column=0, sticky="nw", padx=(0, 5), pady=2)

        # 创建类别选择框架
        classes_frame = ttk.Frame(parent)
        classes_frame.grid(row=start_row, column=1, sticky="ew", padx=(0, 5), pady=2)

        # 定义可用的目标类别
        self.available_classes = {
            0: "人体/躯干",
            1: "头部",
            2: "武器/装备",
            3: "载具",
            4: "其他目标"
        }

        # 从配置文件读取当前选择的类别
        current_classes = self.get_current_target_classes()

        # 创建复选框
        self.target_classes_vars = {}
        for class_id, class_name in self.available_classes.items():
            var = tk.BooleanVar(value=class_id in current_classes)
            self.target_classes_vars[class_id] = var

            checkbox = ttk.Checkbutton(
                classes_frame,
                text=f"{class_id}: {class_name}",
                variable=var,
                command=self.save_target_classes_config
            )
            checkbox.grid(row=class_id//2, column=class_id%2, sticky="w", padx=5, pady=2)

        # 注册热重载支持
        indicator = self.add_hotreload_indicator(parent, 'visual_yolo', 'target_classes')
        indicator.grid(row=start_row, column=2, padx=5)

    def get_current_target_classes(self):
        """从配置文件获取当前的目标类别设置."""
        try:
            classes_str = self.config.get('visual_yolo', 'target_classes', fallback='[0, 1, 2]')
            # 解析类别列表
            import ast
            classes_list = ast.literal_eval(classes_str)
            return set(classes_list)
        except Exception as e:
            print(f"⚠️ 解析target_classes失败: {e}")
            return {0, 1, 2}  # 默认类别

    def save_target_classes_config(self):
        """保存目标类别配置."""
        try:
            # 获取选中的类别
            selected_classes = []
            for class_id, var in self.target_classes_vars.items():
                if var.get():
                    selected_classes.append(class_id)

            # 确保至少选择一个类别
            if not selected_classes:
                messagebox.showwarning("警告", "至少需要选择一个目标类别！")
                # 恢复默认选择
                self.target_classes_vars[0].set(True)
                selected_classes = [0]

            # 保存到配置文件
            classes_str = str(selected_classes)
            self.update_config('visual_yolo', 'target_classes', classes_str)

            print(f"✅ 目标类别已更新: {selected_classes}")

        except Exception as e:
            print(f"❌ 保存目标类别失败: {e}")
            messagebox.showerror("错误", f"保存目标类别失败: {e}")

    def refresh_target_classes_from_config(self):
        """从配置文件刷新目标类别选择状态."""
        try:
            current_classes = self.get_current_target_classes()
            for class_id, var in self.target_classes_vars.items():
                var.set(class_id in current_classes)
            print(f"✅ 目标类别GUI状态已刷新: {current_classes}")
        except Exception as e:
            print(f"⚠️ 刷新目标类别GUI状态失败: {e}")

    def refresh_performance_stats(self, event=None):
        """刷新性能统计信息."""
        try:
            if hasattr(self, 'performance_monitor') and self.performance_monitor:
                # 更新性能监控数据
                self.performance_monitor.update_memory_usage()
                print("✅ 性能统计已刷新")
            else:
                print("⚠️ 性能监控器未初始化")
        except Exception as e:
            print(f"⚠️ 刷新性能统计失败: {e}")

    def create_control_tab(self, notebook):
        """创建控制设置标签页."""
        # 创建滚动框架
        frame = self.create_scrollable_frame(notebook, "控制设置")

        # 后坐力控制区域
        compensation_frame = ttk.LabelFrame(frame, text="后坐力控制", padding=10)
        compensation_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        compensation_frame.columnconfigure(1, weight=1)

        # 补偿模式 (固定为move模式)
        ttk.Label(compensation_frame, text="补偿模式:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        mode_frame = ttk.Frame(compensation_frame)
        mode_frame.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        mode_frame.columnconfigure(0, weight=1)

        # 固定显示move模式
        ttk.Label(mode_frame, text="move (直接补偿)", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky="w")
        ttk.Label(mode_frame, text="💡 达到最大偏移量后停止移动，松开左键后自动恢复",
                 font=("Arial", 8), foreground="gray").grid(row=1, column=0, sticky="w")

        # X轴补偿
        ttk.Label(compensation_frame, text="X轴补偿:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        comp_x_frame = ttk.Frame(compensation_frame)
        comp_x_frame.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        comp_x_frame.columnconfigure(0, weight=1)

        self.comp_x_var = tk.DoubleVar(value=float(self.config.get('compensation', 'compensation_x', fallback='0.0')))
        comp_x_scale = ttk.Scale(comp_x_frame, from_=-500.0, to=500.0, variable=self.comp_x_var,
                               orient=tk.HORIZONTAL, length=200)
        comp_x_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        self.comp_x_display_var = tk.StringVar(value=f"{self.comp_x_var.get():.1f}")
        comp_x_entry = ttk.Entry(comp_x_frame, textvariable=self.comp_x_display_var, width=8)
        comp_x_entry.grid(row=0, column=1, padx=(0, 5))

        comp_x_scale.bind('<Motion>', lambda e: self.update_comp_x_display())
        comp_x_scale.bind('<ButtonRelease-1>', lambda e: self.save_comp_x_config())
        comp_x_entry.bind('<Return>', lambda e: self.update_comp_x_from_entry())
        comp_x_entry.bind('<FocusOut>', lambda e: self.update_comp_x_from_entry())

        # Y轴补偿
        ttk.Label(compensation_frame, text="Y轴补偿:").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        comp_y_frame = ttk.Frame(compensation_frame)
        comp_y_frame.grid(row=2, column=1, sticky="ew", padx=5, pady=5)
        comp_y_frame.columnconfigure(0, weight=1)

        self.comp_y_var = tk.DoubleVar(value=float(self.config.get('compensation', 'compensation_y', fallback='0.0')))
        comp_y_scale = ttk.Scale(comp_y_frame, from_=-1000.0, to=1000.0, variable=self.comp_y_var,
                               orient=tk.HORIZONTAL, length=200)
        comp_y_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        self.comp_y_display_var = tk.StringVar(value=f"{self.comp_y_var.get():.1f}")
        comp_y_entry = ttk.Entry(comp_y_frame, textvariable=self.comp_y_display_var, width=8)
        comp_y_entry.grid(row=0, column=1, padx=(0, 5))

        comp_y_scale.bind('<Motion>', lambda e: self.update_comp_y_display())
        comp_y_scale.bind('<ButtonRelease-1>', lambda e: self.save_comp_y_config())
        comp_y_entry.bind('<Return>', lambda e: self.update_comp_y_from_entry())
        comp_y_entry.bind('<FocusOut>', lambda e: self.update_comp_y_from_entry())

        # 最大偏移量
        ttk.Label(compensation_frame, text="最大偏移量:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        self.max_offset_var = tk.IntVar(value=int(self.config.get('compensation', 'max_offset', fallback='100')))
        max_offset_spin = ttk.Spinbox(compensation_frame, from_=10, to=2000, textvariable=self.max_offset_var, width=10)
        max_offset_spin.grid(row=3, column=1, sticky="w", padx=5, pady=5)
        max_offset_spin.bind('<FocusOut>',
                           lambda e: self.update_config('compensation', 'max_offset', str(self.max_offset_var.get())))

        # 恢复速率
        ttk.Label(compensation_frame, text="恢复速率:").grid(row=4, column=0, sticky="w", padx=5, pady=5)
        recovery_frame = ttk.Frame(compensation_frame)
        recovery_frame.grid(row=4, column=1, sticky="ew", padx=5, pady=5)
        recovery_frame.columnconfigure(0, weight=1)

        self.recovery_rate_var = tk.DoubleVar(value=float(self.config.get('compensation', 'recovery_rate', fallback='0.0')))
        recovery_scale = ttk.Scale(recovery_frame, from_=0.0, to=1.0, variable=self.recovery_rate_var,
                                 orient=tk.HORIZONTAL, length=200)
        recovery_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        self.recovery_rate_display_var = tk.StringVar(value=f"{self.recovery_rate_var.get():.2f}")
        recovery_entry = ttk.Entry(recovery_frame, textvariable=self.recovery_rate_display_var, width=8)
        recovery_entry.grid(row=0, column=1, padx=(0, 5))

        recovery_scale.bind('<Motion>', lambda e: self.update_recovery_rate_display())
        recovery_scale.bind('<ButtonRelease-1>', lambda e: self.save_recovery_rate_config())
        recovery_entry.bind('<Return>', lambda e: self.update_recovery_rate_from_entry())
        recovery_entry.bind('<FocusOut>', lambda e: self.update_recovery_rate_from_entry())

        # 🔧 延时压枪控制区域
        delay_recoil_frame = ttk.LabelFrame(frame, text="⏱️ 延时压枪控制", padding=10)
        delay_recoil_frame.grid(row=0, column=2, sticky="ew", padx=5, pady=5)
        delay_recoil_frame.columnconfigure(1, weight=1)

        # 延时时间
        ttk.Label(delay_recoil_frame, text="延时时间(ms):").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        delay_frame = ttk.Frame(delay_recoil_frame)
        delay_frame.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
        delay_frame.columnconfigure(0, weight=1)

        self.delay_compensation_var = tk.IntVar(value=int(self.config.get('compensation', 'delay_before_compensation', fallback='300')))
        delay_scale = ttk.Scale(delay_frame, from_=0, to=2000, variable=self.delay_compensation_var,
                               orient=tk.HORIZONTAL, length=150)
        delay_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        self.delay_display_var = tk.StringVar(value=f"{self.delay_compensation_var.get()}")
        delay_entry = ttk.Entry(delay_frame, textvariable=self.delay_display_var, width=6)
        delay_entry.grid(row=0, column=1, padx=(0, 5))

        delay_scale.bind('<Motion>', lambda e: self.update_delay_compensation_display())
        delay_scale.bind('<ButtonRelease-1>', lambda e: self.save_delay_compensation_config())
        delay_entry.bind('<Return>', lambda e: self.update_delay_compensation_from_entry())
        delay_entry.bind('<FocusOut>', lambda e: self.update_delay_compensation_from_entry())

        # 增强倍数
        ttk.Label(delay_recoil_frame, text="增强倍数:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        multiplier_frame = ttk.Frame(delay_recoil_frame)
        multiplier_frame.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
        multiplier_frame.columnconfigure(0, weight=1)

        self.enhanced_multiplier_var = tk.DoubleVar(value=float(self.config.get('compensation', 'enhanced_compensation_multiplier', fallback='2.5')))
        multiplier_scale = ttk.Scale(multiplier_frame, from_=1.0, to=10.0, variable=self.enhanced_multiplier_var,
                                    orient=tk.HORIZONTAL, length=150)
        multiplier_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        self.multiplier_display_var = tk.StringVar(value=f"{self.enhanced_multiplier_var.get():.1f}")
        multiplier_entry = ttk.Entry(multiplier_frame, textvariable=self.multiplier_display_var, width=6)
        multiplier_entry.grid(row=0, column=1, padx=(0, 5))

        multiplier_scale.bind('<Motion>', lambda e: self.update_enhanced_multiplier_display())
        multiplier_scale.bind('<ButtonRelease-1>', lambda e: self.save_enhanced_multiplier_config())
        multiplier_entry.bind('<Return>', lambda e: self.update_enhanced_multiplier_from_entry())
        multiplier_entry.bind('<FocusOut>', lambda e: self.update_enhanced_multiplier_from_entry())

        # 轻微补偿系数
        ttk.Label(delay_recoil_frame, text="延时期间补偿:").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        light_frame = ttk.Frame(delay_recoil_frame)
        light_frame.grid(row=2, column=1, sticky="ew", padx=5, pady=5)
        light_frame.columnconfigure(0, weight=1)

        self.light_compensation_var = tk.DoubleVar(value=float(self.config.get('compensation', 'light_compensation_factor', fallback='0.3')))
        light_scale = ttk.Scale(light_frame, from_=0.0, to=1.0, variable=self.light_compensation_var,
                               orient=tk.HORIZONTAL, length=150)
        light_scale.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        self.light_display_var = tk.StringVar(value=f"{self.light_compensation_var.get():.2f}")
        light_entry = ttk.Entry(light_frame, textvariable=self.light_display_var, width=6)
        light_entry.grid(row=0, column=1, padx=(0, 5))

        light_scale.bind('<Motion>', lambda e: self.update_light_compensation_display())
        light_scale.bind('<ButtonRelease-1>', lambda e: self.save_light_compensation_config())
        light_entry.bind('<Return>', lambda e: self.update_light_compensation_from_entry())
        light_entry.bind('<FocusOut>', lambda e: self.update_light_compensation_from_entry())

        # 说明文本
        info_label = ttk.Label(delay_recoil_frame, text="💡 延时期间补偿: 0=无补偿, 1=全补偿 | 增强倍数: 延时后的强度倍数",
                              font=('Arial', 8), foreground='gray')
        info_label.grid(row=3, column=0, columnspan=2, sticky="w", padx=5, pady=5)

        # 扳机控制区域
        action_frame = ttk.LabelFrame(frame, text="扳机控制", padding=10)
        action_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        action_frame.columnconfigure(1, weight=1)

        # 动作延迟
        ttk.Label(action_frame, text="动作延迟 (ms):").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.action_delay_var = tk.IntVar(value=int(self.config.get('action', 'action_delay', fallback='0')))
        action_delay_spin = ttk.Spinbox(action_frame, from_=0, to=1000, textvariable=self.action_delay_var, width=10)
        action_delay_spin.grid(row=0, column=1, sticky="w", padx=5, pady=5)
        action_delay_spin.bind('<FocusOut>',
                             lambda e: self.update_config('action', 'action_delay', str(self.action_delay_var.get())))

        # 动作随机化
        ttk.Label(action_frame, text="动作随机化 (%):").grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.action_random_var = tk.IntVar(value=int(self.config.get('action', 'action_randomization', fallback='30')))
        action_random_spin = ttk.Spinbox(action_frame, from_=0, to=100, textvariable=self.action_random_var, width=10)
        action_random_spin.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        action_random_spin.bind('<FocusOut>',
                              lambda e: self.update_config('action', 'action_randomization', str(self.action_random_var.get())))

        # 动作阈值
        ttk.Label(action_frame, text="动作阈值 (像素):").grid(row=2, column=0, sticky="w", padx=5, pady=5)
        threshold_frame = ttk.Frame(action_frame)
        threshold_frame.grid(row=2, column=1, sticky="ew", padx=5, pady=5)

        self.action_threshold_var = tk.IntVar(value=int(self.config.get('action', 'action_threshold', fallback='8')))
        action_threshold_spin = ttk.Spinbox(threshold_frame, from_=1, to=50, textvariable=self.action_threshold_var, width=10)
        action_threshold_spin.grid(row=0, column=0, sticky="w")
        action_threshold_spin.bind('<FocusOut>',
                                 lambda e: self.update_config('action', 'action_threshold', str(self.action_threshold_var.get())))

        # 添加说明标签
        ttk.Label(threshold_frame, text="(目标中心到屏幕中心的最大距离)", font=("Arial", 8)).grid(row=0, column=1, sticky="w", padx=(5, 0))

        frame.columnconfigure(1, weight=1)

    def create_config_management_tab(self, notebook):
        """创建配置管理标签页."""
        # 创建滚动框架
        frame = self.create_scrollable_frame(notebook, "配置管理")

        # 当前配置信息区域
        current_frame = ttk.LabelFrame(frame, text="当前配置", padding=10)
        current_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        current_frame.columnconfigure(1, weight=1)

        ttk.Label(current_frame, text="当前配置文件:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.current_config_label = ttk.Label(current_frame, text=self.current_config_name,
                                            font=("Arial", 10, "bold"), foreground="blue")
        self.current_config_label.grid(row=0, column=1, sticky="w", padx=5, pady=5)

        # 配置文件列表区域
        list_frame = ttk.LabelFrame(frame, text="可用配置文件", padding=10)
        list_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        list_frame.columnconfigure(0, weight=1)

        # 配置文件列表
        list_container = ttk.Frame(list_frame)
        list_container.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        list_container.columnconfigure(0, weight=1)

        # 列表框和滚动条
        list_scroll_frame = ttk.Frame(list_container)
        list_scroll_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        list_scroll_frame.columnconfigure(0, weight=1)

        self.config_listbox = tk.Listbox(list_scroll_frame, height=8, selectmode=tk.SINGLE)
        self.config_listbox.grid(row=0, column=0, sticky="ew")

        list_scrollbar = ttk.Scrollbar(list_scroll_frame, orient=tk.VERTICAL, command=self.config_listbox.yview)
        list_scrollbar.grid(row=0, column=1, sticky="ns")
        self.config_listbox.config(yscrollcommand=list_scrollbar.set)

        # 填充配置文件列表
        self.refresh_config_list()

        # 配置操作按钮区域
        button_frame = ttk.Frame(list_frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_row1, text="📂 加载配置", command=self.load_selected_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="💾 另存为", command=self.save_config_as).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row1, text="📁 导入配置", command=self.import_config_file).pack(side=tk.LEFT, padx=(0, 5))

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X)

        ttk.Button(button_row2, text="✏️ 重命名", command=self.rename_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row2, text="🗑️ 删除", command=self.delete_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_row2, text="🔄 刷新列表", command=self.refresh_config_list).pack(side=tk.LEFT, padx=(0, 5))

        frame.columnconfigure(1, weight=1)

    # 移除进程控制区域，使用传统配置模式

        # 移除实时参数调整区域，使用传统配置模式

    def create_control_buttons(self, parent):
        """创建控制按钮."""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=10)

        # 第一行按钮
        button_row1 = ttk.Frame(control_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_row1, text="💾 保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row1, text="🔄 重载配置", command=self.reload_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row1, text="📋 备份配置", command=self.backup_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row1, text="↩️ 恢复配置", command=self.restore_config).pack(side=tk.LEFT, padx=5)

        # 快速配置切换
        quick_frame = ttk.Frame(button_row1)
        quick_frame.pack(side=tk.RIGHT, padx=5)

        ttk.Label(quick_frame, text="快速切换:").pack(side=tk.LEFT, padx=(0, 5))
        self.quick_config_var = tk.StringVar(value=self.current_config_name)
        quick_combo = ttk.Combobox(quick_frame, textvariable=self.quick_config_var,
                                 values=self.available_configs, width=15, state="readonly")
        quick_combo.pack(side=tk.LEFT, padx=(0, 5))
        quick_combo.bind('<<ComboboxSelected>>', self.on_quick_config_change)

        # 第二行按钮
        button_row2 = ttk.Frame(control_frame)
        button_row2.pack(fill=tk.X)

        # 左侧功能按钮
        ttk.Button(button_row2, text="🔄 重启程序", command=self.restart_main_program).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row2, text="🔒 锁定运行时控件", command=self.apply_widget_states_after_startup).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row2, text="🔓 解锁所有控件", command=self.unlock_all_widgets).pack(side=tk.LEFT, padx=5)

        # 右侧控制按钮
        ttk.Button(button_row2, text="🔒 隐藏界面", command=self.hide_window).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_row2, text="❌ 退出", command=self.quit_app).pack(side=tk.RIGHT, padx=5)

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(control_frame, textvariable=self.status_var)
        status_label.pack(side=tk.RIGHT, padx=20)
        
    def update_config(self, section, option, value):
        """Update configuration."""
        try:
            if not self.config.has_section(section):
                self.config.add_section(section)
            self.config.set(section, option, value)
            self.save_config()
            print(f"✅ 更新配置: {section}.{option} = {value}")
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")

    def update_config_input_method(self, value):
        """更新输入方法配置，使用新的配置结构."""
        try:
            # 确保[input]节存在
            if not self.config.has_section('input'):
                self.config.add_section('input')

            # 保存到新的[input]节
            self.config.set('input', 'input_method', value)

            # 为了向后兼容，也保存到[enhancement]节
            if not self.config.has_section('enhancement'):
                self.config.add_section('enhancement')
            self.config.set('enhancement', 'input_method', value)

            # 保存配置文件
            self.save_config()
            print(f"✅ 输入方法已更新: {value}")

        except Exception as e:
            print(f"❌ 更新输入方法配置失败: {e}")

    def update_input_method_info(self):
        """更新输入方法说明信息."""
        if not hasattr(self, 'input_info_var'):
            return

        current_method = self.input_var.get()

        # 输入方法说明信息（添加KmBoxNet网络硬件方案）
        method_info = {
            "kmboxnet": "🏆 KmBoxNet网络硬件 - 反作弊最强，VALORANT可用",
            "sendinput": "⚠️ SendInput API - 可能被反作弊系统阻止",
            "thirdparty": "🥇 第三方库 - 多重保护，推荐使用",
            "winapi": "⚠️ Win32 API - 可能被游戏反作弊阻止",
            "interception": "🔧 Interception驱动 - 需要安装驱动程序",
            "microcontroller_serial": "🏆 微控制器串口 - 最安全，需硬件",
            "microcontroller_socket": "🏆 微控制器网络 - 最安全，需硬件"
        }

        info_text = method_info.get(current_method, "未知输入方法")
        self.input_info_var.set(info_text)

    # 滑块控件相关方法
    def update_speed_display(self):
        """更新速度显示."""
        if self.speed_display_var:
            self.speed_display_var.set(f"{self.speed_var.get():.2f}")

    def save_speed_config(self):
        """保存速度配置."""
        self.update_config('enhancement', 'movement_speed', f"{self.speed_var.get():.2f}")

    def update_speed_from_entry(self):
        """从输入框更新速度."""
        try:
            value = float(self.speed_display_var.get())
            if 0.1 <= value <= 3.0:
                self.speed_var.set(value)
                self.save_speed_config()
            else:
                # 恢复原值
                self.speed_display_var.set(f"{self.speed_var.get():.2f}")
                messagebox.showwarning("警告", "速度值必须在0.1-3.0之间")
        except ValueError:
            # 恢复原值
            self.speed_display_var.set(f"{self.speed_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    def update_conf_display(self):
        """更新置信度显示."""
        if self.conf_display_var:
            self.conf_display_var.set(f"{self.conf_var.get():.2f}")

    def save_conf_config(self):
        """保存置信度配置."""
        self.update_config('visual_yolo', 'confidence_threshold', f"{self.conf_var.get():.2f}")

    def update_conf_from_entry(self):
        """从输入框更新置信度."""
        try:
            value = float(self.conf_display_var.get())
            if 0.1 <= value <= 0.9:
                self.conf_var.set(value)
                self.save_conf_config()
            else:
                # 恢复原值
                self.conf_display_var.set(f"{self.conf_var.get():.2f}")
                messagebox.showwarning("警告", "置信度值必须在0.1-0.9之间")
        except ValueError:
            # 恢复原值
            self.conf_display_var.set(f"{self.conf_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # 目标高度比例相关方法
    def update_height_ratio_display(self):
        """更新目标高度比例显示."""
        if self.height_ratio_display_var:
            self.height_ratio_display_var.set(f"{self.height_ratio_var.get():.2f}")

    def save_height_ratio_config(self):
        """保存目标高度比例配置."""
        self.update_config('enhancement', 'target_height_ratio', f"{self.height_ratio_var.get():.2f}")

    def update_height_ratio_from_entry(self):
        """从输入框更新目标高度比例."""
        try:
            value = float(self.height_ratio_display_var.get())
            if 0.1 <= value <= 1.0:
                self.height_ratio_var.set(value)
                self.save_height_ratio_config()
            else:
                self.height_ratio_display_var.set(f"{self.height_ratio_var.get():.2f}")
                messagebox.showwarning("警告", "目标高度比例必须在0.1-1.0之间")
        except ValueError:
            self.height_ratio_display_var.set(f"{self.height_ratio_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # 移动平滑因子相关方法
    def update_smoothing_display(self):
        """更新移动平滑因子显示."""
        if self.smoothing_display_var:
            self.smoothing_display_var.set(f"{self.smoothing_var.get():.2f}")

    def save_smoothing_config(self):
        """保存移动平滑因子配置."""
        self.update_config('movement', 'movement_smoothing_factor', f"{self.smoothing_var.get():.2f}")

    def update_smoothing_from_entry(self):
        """从输入框更新移动平滑因子."""
        try:
            value = float(self.smoothing_display_var.get())
            if 0.1 <= value <= 0.8:
                self.smoothing_var.set(value)
                self.save_smoothing_config()
            else:
                self.smoothing_display_var.set(f"{self.smoothing_var.get():.2f}")
                messagebox.showwarning("警告", "移动平滑因子必须在0.1-0.8之间")
        except ValueError:
            self.smoothing_display_var.set(f"{self.smoothing_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # 控制方法相关方法
    def save_control_method(self):
        """保存控制方法配置."""
        method = self.control_method_var.get()
        self.update_config('movement', 'control_method', method)
        self.update_pid_config_visibility()

    def create_pid_config_section(self, parent_frame):
        """创建PID配置区域."""
        # PID配置框架 - 修复布局重叠问题
        self.pid_config_frame = ttk.LabelFrame(parent_frame, text="🎯 PID控制参数", padding="15")
        self.pid_config_frame.grid(row=10, column=0, columnspan=2, sticky="ew", padx=10, pady=(15, 10))
        self.pid_config_frame.columnconfigure(1, weight=1)

        # Kp参数 - 改进布局
        kp_label = ttk.Label(self.pid_config_frame, text="比例增益 (Kp):", font=("Arial", 9))
        kp_label.grid(row=0, column=0, sticky="w", padx=(5, 10), pady=5)

        kp_frame = ttk.Frame(self.pid_config_frame)
        kp_frame.grid(row=0, column=1, sticky="ew", padx=(0, 5), pady=5)
        kp_frame.columnconfigure(0, weight=1)

        self.kp_var = tk.DoubleVar(value=float(self.config.get('movement', 'pid_kp', fallback='0.8')))
        kp_scale = ttk.Scale(kp_frame, from_=0.1, to=2.0, variable=self.kp_var, orient=tk.HORIZONTAL, length=200)
        kp_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.kp_display_var = tk.StringVar(value=f"{self.kp_var.get():.2f}")
        kp_entry = ttk.Entry(kp_frame, textvariable=self.kp_display_var, width=8, justify='center')
        kp_entry.grid(row=0, column=1)

        # 添加说明标签
        kp_tip = ttk.Label(self.pid_config_frame, text="控制响应速度 (0.1-2.0)",
                          font=("Arial", 8), foreground="gray")
        kp_tip.grid(row=0, column=2, sticky="w", padx=(10, 5), pady=5)

        kp_scale.bind('<Motion>', lambda e: self.update_kp_display())
        kp_scale.bind('<ButtonRelease-1>', lambda e: self.save_kp_config())
        kp_entry.bind('<Return>', lambda e: self.update_kp_from_entry())
        kp_entry.bind('<FocusOut>', lambda e: self.update_kp_from_entry())

        # Ki参数 - 改进布局
        ki_label = ttk.Label(self.pid_config_frame, text="积分增益 (Ki):", font=("Arial", 9))
        ki_label.grid(row=1, column=0, sticky="w", padx=(5, 10), pady=5)

        ki_frame = ttk.Frame(self.pid_config_frame)
        ki_frame.grid(row=1, column=1, sticky="ew", padx=(0, 5), pady=5)
        ki_frame.columnconfigure(0, weight=1)

        self.ki_var = tk.DoubleVar(value=float(self.config.get('movement', 'pid_ki', fallback='0.1')))
        ki_scale = ttk.Scale(ki_frame, from_=0.0, to=0.5, variable=self.ki_var, orient=tk.HORIZONTAL, length=200)
        ki_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.ki_display_var = tk.StringVar(value=f"{self.ki_var.get():.3f}")
        ki_entry = ttk.Entry(ki_frame, textvariable=self.ki_display_var, width=8, justify='center')
        ki_entry.grid(row=0, column=1)

        # 添加说明标签
        ki_tip = ttk.Label(self.pid_config_frame, text="消除稳态误差 (0.0-0.5)",
                          font=("Arial", 8), foreground="gray")
        ki_tip.grid(row=1, column=2, sticky="w", padx=(10, 5), pady=5)

        ki_scale.bind('<Motion>', lambda e: self.update_ki_display())
        ki_scale.bind('<ButtonRelease-1>', lambda e: self.save_ki_config())
        ki_entry.bind('<Return>', lambda e: self.update_ki_from_entry())
        ki_entry.bind('<FocusOut>', lambda e: self.update_ki_from_entry())

        # Kd参数 - 改进布局
        kd_label = ttk.Label(self.pid_config_frame, text="微分增益 (Kd):", font=("Arial", 9))
        kd_label.grid(row=2, column=0, sticky="w", padx=(5, 10), pady=5)

        kd_frame = ttk.Frame(self.pid_config_frame)
        kd_frame.grid(row=2, column=1, sticky="ew", padx=(0, 5), pady=5)
        kd_frame.columnconfigure(0, weight=1)

        self.kd_var = tk.DoubleVar(value=float(self.config.get('movement', 'pid_kd', fallback='0.2')))
        kd_scale = ttk.Scale(kd_frame, from_=0.0, to=1.0, variable=self.kd_var, orient=tk.HORIZONTAL, length=200)
        kd_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.kd_display_var = tk.StringVar(value=f"{self.kd_var.get():.3f}")
        kd_entry = ttk.Entry(kd_frame, textvariable=self.kd_display_var, width=8, justify='center')
        kd_entry.grid(row=0, column=1)

        # 添加说明标签
        kd_tip = ttk.Label(self.pid_config_frame, text="减少振荡超调 (0.0-1.0)",
                          font=("Arial", 8), foreground="gray")
        kd_tip.grid(row=2, column=2, sticky="w", padx=(10, 5), pady=5)

        kd_scale.bind('<Motion>', lambda e: self.update_kd_display())
        kd_scale.bind('<ButtonRelease-1>', lambda e: self.save_kd_config())
        kd_entry.bind('<Return>', lambda e: self.update_kd_from_entry())
        kd_entry.bind('<FocusOut>', lambda e: self.update_kd_from_entry())

        # 分隔线
        separator = ttk.Separator(self.pid_config_frame, orient='horizontal')
        separator.grid(row=3, column=0, columnspan=3, sticky="ew", padx=5, pady=(10, 5))

        # PID调试和预设区域
        debug_frame = ttk.Frame(self.pid_config_frame)
        debug_frame.grid(row=4, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        debug_frame.columnconfigure(1, weight=1)

        # PID调试开关
        self.pid_debug_var = tk.BooleanVar(value=self.config.getboolean('movement', 'debug_pid', fallback=False))
        pid_debug_check = ttk.Checkbutton(debug_frame, text="🔍 启用PID调试输出",
                                        variable=self.pid_debug_var, command=self.save_pid_debug_config)
        pid_debug_check.grid(row=0, column=0, sticky="w", padx=(0, 20))

        # 智能预设按钮
        preset_frame = ttk.Frame(debug_frame)
        preset_frame.grid(row=0, column=1, sticky="e")

        ttk.Button(preset_frame, text="🚀 极速", width=8,
                  command=lambda: self.apply_enhanced_preset('ultra_fast')).pack(side=tk.LEFT, padx=2)
        ttk.Button(preset_frame, text="⚖️ 智能", width=8,
                  command=lambda: self.apply_enhanced_preset('intelligent')).pack(side=tk.LEFT, padx=2)
        ttk.Button(preset_frame, text="🎯 精准", width=8,
                  command=lambda: self.apply_enhanced_preset('precision')).pack(side=tk.LEFT, padx=2)

        # PID状态显示
        self.pid_status_var = tk.StringVar(value="PID控制器就绪")
        pid_status_label = ttk.Label(self.pid_config_frame, textvariable=self.pid_status_var,
                                   font=("Arial", 8), foreground="green")
        pid_status_label.grid(row=5, column=0, columnspan=3, sticky="w", padx=5, pady=(5, 0))

        # 配置列权重
        self.pid_config_frame.columnconfigure(1, weight=1)
        self.pid_config_frame.columnconfigure(2, weight=0)

        # 初始化显示状态
        self.update_pid_config_visibility()

    def create_enhanced_movement_config_section(self, parent_frame):
        """创建智能移动算法配置区域."""
        # 智能移动配置框架
        self.enhanced_movement_frame = ttk.LabelFrame(parent_frame, text="🚀 智能移动算法参数", padding="15")
        self.enhanced_movement_frame.grid(row=11, column=0, columnspan=2, sticky="ew", padx=10, pady=(15, 10))
        self.enhanced_movement_frame.columnconfigure(1, weight=1)

        # 速度缩放参数 (X轴)
        velocity_label = ttk.Label(self.enhanced_movement_frame, text="速度缩放 (X轴):", font=("Arial", 9))
        velocity_label.grid(row=0, column=0, sticky="w", padx=(5, 10), pady=5)

        velocity_frame = ttk.Frame(self.enhanced_movement_frame)
        velocity_frame.grid(row=0, column=1, sticky="ew", padx=(0, 5), pady=5)
        velocity_frame.columnconfigure(0, weight=1)

        self.velocity_scale_x_var = tk.DoubleVar(value=float(self.config.get('movement', 'velocity_scale_x', fallback='1.2')))
        velocity_scale = ttk.Scale(velocity_frame, from_=0.5, to=3.0, variable=self.velocity_scale_x_var, orient=tk.HORIZONTAL, length=200)
        velocity_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.velocity_scale_x_display_var = tk.StringVar(value=f"{self.velocity_scale_x_var.get():.2f}")
        velocity_entry = ttk.Entry(velocity_frame, textvariable=self.velocity_scale_x_display_var, width=8, justify='center')
        velocity_entry.grid(row=0, column=1)

        velocity_tip = ttk.Label(self.enhanced_movement_frame, text="水平移动速度倍率 (0.5-3.0)",
                               font=("Arial", 8), foreground="gray")
        velocity_tip.grid(row=0, column=2, sticky="w", padx=(10, 5), pady=5)

        velocity_scale.bind('<Motion>', lambda e: self.update_velocity_scale_x_display())
        velocity_scale.bind('<ButtonRelease-1>', lambda e: self.save_velocity_scale_x_config())
        velocity_entry.bind('<Return>', lambda e: self.update_velocity_scale_x_from_entry())
        velocity_entry.bind('<FocusOut>', lambda e: self.update_velocity_scale_x_from_entry())

        # Y轴速度缩放参数
        velocity_y_label = ttk.Label(self.enhanced_movement_frame, text="速度缩放 (Y轴):", font=("Arial", 9))
        velocity_y_label.grid(row=1, column=0, sticky="w", padx=(5, 10), pady=5)

        velocity_y_frame = ttk.Frame(self.enhanced_movement_frame)
        velocity_y_frame.grid(row=1, column=1, sticky="ew", padx=(0, 5), pady=5)
        velocity_y_frame.columnconfigure(0, weight=1)

        self.velocity_scale_y_var = tk.DoubleVar(value=float(self.config.get('movement', 'velocity_scale_y', fallback='1.0')))
        velocity_y_scale = ttk.Scale(velocity_y_frame, from_=0.5, to=3.0, variable=self.velocity_scale_y_var, orient=tk.HORIZONTAL, length=200)
        velocity_y_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.velocity_scale_y_display_var = tk.StringVar(value=f"{self.velocity_scale_y_var.get():.2f}")
        velocity_y_entry = ttk.Entry(velocity_y_frame, textvariable=self.velocity_scale_y_display_var, width=8, justify='center')
        velocity_y_entry.grid(row=0, column=1)

        velocity_y_tip = ttk.Label(self.enhanced_movement_frame, text="垂直移动速度倍率 (0.5-3.0)",
                                 font=("Arial", 8), foreground="gray")
        velocity_y_tip.grid(row=1, column=2, sticky="w", padx=(10, 5), pady=5)

        velocity_y_scale.bind('<Motion>', lambda e: self.update_velocity_scale_y_display())
        velocity_y_scale.bind('<ButtonRelease-1>', lambda e: self.save_velocity_scale_y_config())
        velocity_y_entry.bind('<Return>', lambda e: self.update_velocity_scale_y_from_entry())
        velocity_y_entry.bind('<FocusOut>', lambda e: self.update_velocity_scale_y_from_entry())

        # 动态增强倍率参数
        boost_label = ttk.Label(self.enhanced_movement_frame, text="动态增强倍率:", font=("Arial", 9))
        boost_label.grid(row=2, column=0, sticky="w", padx=(5, 10), pady=5)

        boost_frame = ttk.Frame(self.enhanced_movement_frame)
        boost_frame.grid(row=2, column=1, sticky="ew", padx=(0, 5), pady=5)
        boost_frame.columnconfigure(0, weight=1)

        self.dynamic_boost_var = tk.DoubleVar(value=float(self.config.get('movement', 'dynamic_speed_boost', fallback='2.0')))
        boost_scale = ttk.Scale(boost_frame, from_=1.0, to=5.0, variable=self.dynamic_boost_var, orient=tk.HORIZONTAL, length=200)
        boost_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.dynamic_boost_display_var = tk.StringVar(value=f"{self.dynamic_boost_var.get():.2f}")
        boost_entry = ttk.Entry(boost_frame, textvariable=self.dynamic_boost_display_var, width=8, justify='center')
        boost_entry.grid(row=0, column=1)

        boost_tip = ttk.Label(self.enhanced_movement_frame, text="远距离移动增强倍率 (1.0-5.0)",
                            font=("Arial", 8), foreground="gray")
        boost_tip.grid(row=2, column=2, sticky="w", padx=(10, 5), pady=5)

        boost_scale.bind('<Motion>', lambda e: self.update_dynamic_boost_display())
        boost_scale.bind('<ButtonRelease-1>', lambda e: self.save_dynamic_boost_config())
        boost_entry.bind('<Return>', lambda e: self.update_dynamic_boost_from_entry())
        boost_entry.bind('<FocusOut>', lambda e: self.update_dynamic_boost_from_entry())

        # 减速区域参数
        decel_label = ttk.Label(self.enhanced_movement_frame, text="减速区域半径:", font=("Arial", 9))
        decel_label.grid(row=3, column=0, sticky="w", padx=(5, 10), pady=5)

        decel_frame = ttk.Frame(self.enhanced_movement_frame)
        decel_frame.grid(row=3, column=1, sticky="ew", padx=(0, 5), pady=5)
        decel_frame.columnconfigure(0, weight=1)

        self.deceleration_zone_var = tk.DoubleVar(value=float(self.config.get('movement', 'deceleration_zone', fallback='15')))
        decel_scale = ttk.Scale(decel_frame, from_=5, to=50, variable=self.deceleration_zone_var, orient=tk.HORIZONTAL, length=200)
        decel_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.deceleration_zone_display_var = tk.StringVar(value=f"{self.deceleration_zone_var.get():.0f}")
        decel_entry = ttk.Entry(decel_frame, textvariable=self.deceleration_zone_display_var, width=8, justify='center')
        decel_entry.grid(row=0, column=1)

        decel_tip = ttk.Label(self.enhanced_movement_frame, text="开始减速的距离 (5-50像素)",
                            font=("Arial", 8), foreground="gray")
        decel_tip.grid(row=3, column=2, sticky="w", padx=(10, 5), pady=5)

        decel_scale.bind('<Motion>', lambda e: self.update_deceleration_zone_display())
        decel_scale.bind('<ButtonRelease-1>', lambda e: self.save_deceleration_zone_config())
        decel_entry.bind('<Return>', lambda e: self.update_deceleration_zone_from_entry())
        decel_entry.bind('<FocusOut>', lambda e: self.update_deceleration_zone_from_entry())

        # 精调区域参数
        fine_label = ttk.Label(self.enhanced_movement_frame, text="精调区域半径:", font=("Arial", 9))
        fine_label.grid(row=4, column=0, sticky="w", padx=(5, 10), pady=5)

        fine_frame = ttk.Frame(self.enhanced_movement_frame)
        fine_frame.grid(row=4, column=1, sticky="ew", padx=(0, 5), pady=5)
        fine_frame.columnconfigure(0, weight=1)

        self.fine_tune_zone_var = tk.DoubleVar(value=float(self.config.get('movement', 'fine_tune_zone', fallback='5')))
        fine_scale = ttk.Scale(fine_frame, from_=1, to=20, variable=self.fine_tune_zone_var, orient=tk.HORIZONTAL, length=200)
        fine_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.fine_tune_zone_display_var = tk.StringVar(value=f"{self.fine_tune_zone_var.get():.0f}")
        fine_entry = ttk.Entry(fine_frame, textvariable=self.fine_tune_zone_display_var, width=8, justify='center')
        fine_entry.grid(row=0, column=1)

        fine_tip = ttk.Label(self.enhanced_movement_frame, text="精确调整的距离 (1-20像素)",
                           font=("Arial", 8), foreground="gray")
        fine_tip.grid(row=4, column=2, sticky="w", padx=(10, 5), pady=5)

        fine_scale.bind('<Motion>', lambda e: self.update_fine_tune_zone_display())
        fine_scale.bind('<ButtonRelease-1>', lambda e: self.save_fine_tune_zone_config())
        fine_entry.bind('<Return>', lambda e: self.update_fine_tune_zone_from_entry())
        fine_entry.bind('<FocusOut>', lambda e: self.update_fine_tune_zone_from_entry())

        # 新增：速度因子参数
        speed_factor_label = ttk.Label(self.enhanced_movement_frame, text="速度因子:", font=("Arial", 9))
        speed_factor_label.grid(row=5, column=0, sticky="w", padx=(5, 10), pady=5)

        speed_factor_frame = ttk.Frame(self.enhanced_movement_frame)
        speed_factor_frame.grid(row=5, column=1, sticky="ew", padx=(0, 5), pady=5)
        speed_factor_frame.columnconfigure(0, weight=1)

        self.speed_factor_var = tk.DoubleVar(value=float(self.config.get('movement', 'speed_factor', fallback='0.3')))
        speed_factor_scale = ttk.Scale(speed_factor_frame, from_=0.1, to=1.0, variable=self.speed_factor_var, orient=tk.HORIZONTAL, length=200)
        speed_factor_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.speed_factor_display_var = tk.StringVar(value=f"{self.speed_factor_var.get():.2f}")
        speed_factor_entry = ttk.Entry(speed_factor_frame, textvariable=self.speed_factor_display_var, width=8, justify='center')
        speed_factor_entry.grid(row=0, column=1)

        speed_factor_tip = ttk.Label(self.enhanced_movement_frame, text="整体速度缩放 (0.1-1.0)",
                                   font=("Arial", 8), foreground="gray")
        speed_factor_tip.grid(row=5, column=2, sticky="w", padx=(10, 5), pady=5)

        speed_factor_scale.bind('<Motion>', lambda e: self.update_speed_factor_display())
        speed_factor_scale.bind('<ButtonRelease-1>', lambda e: self.save_speed_factor_config())
        speed_factor_entry.bind('<Return>', lambda e: self.update_speed_factor_from_entry())
        speed_factor_entry.bind('<FocusOut>', lambda e: self.update_speed_factor_from_entry())

        # 新增：最大移动距离参数
        max_move_label = ttk.Label(self.enhanced_movement_frame, text="最大移动距离:", font=("Arial", 9))
        max_move_label.grid(row=6, column=0, sticky="w", padx=(5, 10), pady=5)

        max_move_frame = ttk.Frame(self.enhanced_movement_frame)
        max_move_frame.grid(row=6, column=1, sticky="ew", padx=(0, 5), pady=5)
        max_move_frame.columnconfigure(0, weight=1)

        self.max_move_distance_var = tk.DoubleVar(value=float(self.config.get('movement', 'max_move_distance', fallback='25.0')))
        max_move_scale = ttk.Scale(max_move_frame, from_=10.0, to=127.0, variable=self.max_move_distance_var, orient=tk.HORIZONTAL, length=200)
        max_move_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.max_move_distance_display_var = tk.StringVar(value=f"{self.max_move_distance_var.get():.0f}")
        max_move_entry = ttk.Entry(max_move_frame, textvariable=self.max_move_distance_display_var, width=8, justify='center')
        max_move_entry.grid(row=0, column=1)

        max_move_tip = ttk.Label(self.enhanced_movement_frame, text="单次最大移动像素 (10-127)",
                               font=("Arial", 8), foreground="gray")
        max_move_tip.grid(row=6, column=2, sticky="w", padx=(10, 5), pady=5)

        max_move_scale.bind('<Motion>', lambda e: self.update_max_move_distance_display())
        max_move_scale.bind('<ButtonRelease-1>', lambda e: self.save_max_move_distance_config())
        max_move_entry.bind('<Return>', lambda e: self.update_max_move_distance_from_entry())
        max_move_entry.bind('<FocusOut>', lambda e: self.update_max_move_distance_from_entry())

        # 新增：平滑步数参数
        smoothing_steps_label = ttk.Label(self.enhanced_movement_frame, text="平滑步数:", font=("Arial", 9))
        smoothing_steps_label.grid(row=7, column=0, sticky="w", padx=(5, 10), pady=5)

        smoothing_steps_frame = ttk.Frame(self.enhanced_movement_frame)
        smoothing_steps_frame.grid(row=7, column=1, sticky="ew", padx=(0, 5), pady=5)
        smoothing_steps_frame.columnconfigure(0, weight=1)

        self.movement_smoothing_steps_var = tk.IntVar(value=int(self.config.get('movement', 'movement_smoothing_steps', fallback='3')))
        smoothing_steps_scale = ttk.Scale(smoothing_steps_frame, from_=1, to=10, variable=self.movement_smoothing_steps_var, orient=tk.HORIZONTAL, length=200)
        smoothing_steps_scale.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        self.movement_smoothing_steps_display_var = tk.StringVar(value=f"{self.movement_smoothing_steps_var.get()}")
        smoothing_steps_entry = ttk.Entry(smoothing_steps_frame, textvariable=self.movement_smoothing_steps_display_var, width=8, justify='center')
        smoothing_steps_entry.grid(row=0, column=1)

        smoothing_steps_tip = ttk.Label(self.enhanced_movement_frame, text="移动分解步数 (1-10)",
                                      font=("Arial", 8), foreground="gray")
        smoothing_steps_tip.grid(row=7, column=2, sticky="w", padx=(10, 5), pady=5)

        smoothing_steps_scale.bind('<Motion>', lambda e: self.update_movement_smoothing_steps_display())
        smoothing_steps_scale.bind('<ButtonRelease-1>', lambda e: self.save_movement_smoothing_steps_config())
        smoothing_steps_entry.bind('<Return>', lambda e: self.update_movement_smoothing_steps_from_entry())
        smoothing_steps_entry.bind('<FocusOut>', lambda e: self.update_movement_smoothing_steps_from_entry())

        # 配置列权重
        self.enhanced_movement_frame.columnconfigure(1, weight=1)
        self.enhanced_movement_frame.columnconfigure(2, weight=0)

        # 初始化显示状态
        self.update_enhanced_movement_visibility()

    def update_pid_config_visibility(self):
        """更新PID配置区域的可见性."""
        if hasattr(self, 'pid_config_frame'):
            if self.control_method_var.get() == 'pid':
                self.pid_config_frame.grid()
            else:
                self.pid_config_frame.grid_remove()

        # 同时更新智能移动算法区域的可见性
        self.update_enhanced_movement_visibility()

    def update_enhanced_movement_visibility(self):
        """更新智能移动算法配置区域的可见性."""
        if hasattr(self, 'enhanced_movement_frame'):
            current_method = self.control_method_var.get()
            if current_method == 'streamlined':
                self.enhanced_movement_frame.grid()
            else:
                self.enhanced_movement_frame.grid_remove()

    # PID参数相关方法
    def update_kp_display(self):
        """更新Kp显示."""
        if self.kp_display_var:
            self.kp_display_var.set(f"{self.kp_var.get():.2f}")

    def save_kp_config(self):
        """保存Kp配置."""
        self.update_config('movement', 'pid_kp', f"{self.kp_var.get():.2f}")

    def update_kp_from_entry(self):
        """从输入框更新Kp."""
        try:
            value = float(self.kp_display_var.get())
            if 0.1 <= value <= 2.0:
                self.kp_var.set(value)
                self.save_kp_config()
            else:
                self.kp_display_var.set(f"{self.kp_var.get():.2f}")
                messagebox.showwarning("警告", "Kp必须在0.1-2.0之间")
        except ValueError:
            self.kp_display_var.set(f"{self.kp_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    def update_ki_display(self):
        """更新Ki显示."""
        if self.ki_display_var:
            self.ki_display_var.set(f"{self.ki_var.get():.3f}")

    def save_ki_config(self):
        """保存Ki配置."""
        self.update_config('movement', 'pid_ki', f"{self.ki_var.get():.3f}")

    def update_ki_from_entry(self):
        """从输入框更新Ki."""
        try:
            value = float(self.ki_display_var.get())
            if 0.0 <= value <= 0.5:
                self.ki_var.set(value)
                self.save_ki_config()
            else:
                self.ki_display_var.set(f"{self.ki_var.get():.3f}")
                messagebox.showwarning("警告", "Ki必须在0.0-0.5之间")
        except ValueError:
            self.ki_display_var.set(f"{self.ki_var.get():.3f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    def update_kd_display(self):
        """更新Kd显示."""
        if self.kd_display_var:
            self.kd_display_var.set(f"{self.kd_var.get():.3f}")

    def save_kd_config(self):
        """保存Kd配置."""
        self.update_config('movement', 'pid_kd', f"{self.kd_var.get():.3f}")

    def update_kd_from_entry(self):
        """从输入框更新Kd."""
        try:
            value = float(self.kd_display_var.get())
            if 0.0 <= value <= 1.0:
                self.kd_var.set(value)
                self.save_kd_config()
            else:
                self.kd_display_var.set(f"{self.kd_var.get():.3f}")
                messagebox.showwarning("警告", "Kd必须在0.0-1.0之间")
        except ValueError:
            self.kd_display_var.set(f"{self.kd_var.get():.3f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    def save_pid_debug_config(self):
        """保存PID调试配置."""
        self.update_config('movement', 'debug_pid', str(self.pid_debug_var.get()).lower())
        if self.pid_debug_var.get():
            self.pid_status_var.set("✅ PID调试已启用")
        else:
            self.pid_status_var.set("PID控制器就绪")

    def apply_enhanced_preset(self, preset_type):
        """应用增强型预设配置."""
        try:
            presets = {
                'ultra_fast': {
                    'kp': 1.5,
                    'ki': 0.03,
                    'kd': 0.4,
                    'name': '极速响应模式',
                    'control_method': 'streamlined'
                },
                'intelligent': {
                    'kp': 1.2,
                    'ki': 0.05,
                    'kd': 0.3,
                    'name': '智能平衡模式',
                    'control_method': 'advanced'
                },
                'precision': {
                    'kp': 0.9,
                    'ki': 0.08,
                    'kd': 0.35,
                    'name': '精准控制模式',
                    'control_method': 'advanced'
                }
            }

            if preset_type not in presets:
                return

            preset = presets[preset_type]

            # 更新GUI控件
            self.kp_var.set(preset['kp'])
            self.ki_var.set(preset['ki'])
            self.kd_var.set(preset['kd'])

            # 更新显示
            self.update_kp_display()
            self.update_ki_display()
            self.update_kd_display()

            # 保存配置
            self.save_kp_config()
            self.save_ki_config()
            self.save_kd_config()

            # 同时更新控制方法
            if hasattr(self, 'control_method_var'):
                self.control_method_var.set(preset['control_method'])
                self.save_control_method()

            # 更新状态
            self.pid_status_var.set(f"✅ 已应用{preset['name']}")
            print(f"✅ 增强预设已应用: {preset['name']} (Kp={preset['kp']}, Ki={preset['ki']}, Kd={preset['kd']}, 方法={preset['control_method']})")

        except Exception as e:
            self.pid_status_var.set(f"❌ 预设应用失败: {str(e)}")
            print(f"❌ 增强预设应用失败: {e}")

    # 智能移动算法参数相关方法
    def update_velocity_scale_x_display(self):
        """更新X轴速度缩放显示."""
        if hasattr(self, 'velocity_scale_x_display_var'):
            self.velocity_scale_x_display_var.set(f"{self.velocity_scale_x_var.get():.2f}")

    def save_velocity_scale_x_config(self):
        """保存X轴速度缩放配置."""
        try:
            value = self.velocity_scale_x_var.get()
            self.update_config('movement', 'velocity_scale_x', str(value))
            print(f"✅ X轴速度缩放已保存: {value:.2f}")
        except Exception as e:
            print(f"❌ X轴速度缩放保存失败: {e}")

    def update_velocity_scale_x_from_entry(self):
        """从输入框更新X轴速度缩放."""
        try:
            value = float(self.velocity_scale_x_display_var.get())
            value = max(0.5, min(3.0, value))  # 限制范围
            self.velocity_scale_x_var.set(value)
            self.save_velocity_scale_x_config()
        except ValueError:
            self.velocity_scale_x_display_var.set(f"{self.velocity_scale_x_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值 (0.5-3.0)")

    def update_velocity_scale_y_display(self):
        """更新Y轴速度缩放显示."""
        if hasattr(self, 'velocity_scale_y_display_var'):
            self.velocity_scale_y_display_var.set(f"{self.velocity_scale_y_var.get():.2f}")

    def save_velocity_scale_y_config(self):
        """保存Y轴速度缩放配置."""
        try:
            value = self.velocity_scale_y_var.get()
            self.update_config('movement', 'velocity_scale_y', str(value))
            print(f"✅ Y轴速度缩放已保存: {value:.2f}")
        except Exception as e:
            print(f"❌ Y轴速度缩放保存失败: {e}")

    def update_velocity_scale_y_from_entry(self):
        """从输入框更新Y轴速度缩放."""
        try:
            value = float(self.velocity_scale_y_display_var.get())
            value = max(0.5, min(3.0, value))  # 限制范围
            self.velocity_scale_y_var.set(value)
            self.save_velocity_scale_y_config()
        except ValueError:
            self.velocity_scale_y_display_var.set(f"{self.velocity_scale_y_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值 (0.5-3.0)")

    def update_dynamic_boost_display(self):
        """更新动态增强倍率显示."""
        if hasattr(self, 'dynamic_boost_display_var'):
            self.dynamic_boost_display_var.set(f"{self.dynamic_boost_var.get():.2f}")

    def save_dynamic_boost_config(self):
        """保存动态增强倍率配置."""
        try:
            value = self.dynamic_boost_var.get()
            self.update_config('movement', 'dynamic_speed_boost', str(value))
            print(f"✅ 动态增强倍率已保存: {value:.2f}")
        except Exception as e:
            print(f"❌ 动态增强倍率保存失败: {e}")

    def update_dynamic_boost_from_entry(self):
        """从输入框更新动态增强倍率."""
        try:
            value = float(self.dynamic_boost_display_var.get())
            value = max(1.0, min(5.0, value))  # 限制范围
            self.dynamic_boost_var.set(value)
            self.save_dynamic_boost_config()
        except ValueError:
            self.dynamic_boost_display_var.set(f"{self.dynamic_boost_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值 (1.0-5.0)")

    def update_deceleration_zone_display(self):
        """更新减速区域显示."""
        if hasattr(self, 'deceleration_zone_display_var'):
            self.deceleration_zone_display_var.set(f"{self.deceleration_zone_var.get():.0f}")

    def save_deceleration_zone_config(self):
        """保存减速区域配置."""
        try:
            value = self.deceleration_zone_var.get()
            self.update_config('movement', 'deceleration_zone', str(int(value)))
            print(f"✅ 减速区域已保存: {value:.0f}")
        except Exception as e:
            print(f"❌ 减速区域保存失败: {e}")

    def update_deceleration_zone_from_entry(self):
        """从输入框更新减速区域."""
        try:
            value = float(self.deceleration_zone_display_var.get())
            value = max(5, min(50, value))  # 限制范围
            self.deceleration_zone_var.set(value)
            self.save_deceleration_zone_config()
        except ValueError:
            self.deceleration_zone_display_var.set(f"{self.deceleration_zone_var.get():.0f}")
            messagebox.showwarning("警告", "请输入有效的数值 (5-50)")

    def update_fine_tune_zone_display(self):
        """更新精调区域显示."""
        if hasattr(self, 'fine_tune_zone_display_var'):
            self.fine_tune_zone_display_var.set(f"{self.fine_tune_zone_var.get():.0f}")

    def save_fine_tune_zone_config(self):
        """保存精调区域配置."""
        try:
            value = self.fine_tune_zone_var.get()
            self.update_config('movement', 'fine_tune_zone', str(int(value)))
            print(f"✅ 精调区域已保存: {value:.0f}")
        except Exception as e:
            print(f"❌ 精调区域保存失败: {e}")

    def update_fine_tune_zone_from_entry(self):
        """从输入框更新精调区域."""
        try:
            value = float(self.fine_tune_zone_display_var.get())
            value = max(1, min(20, value))  # 限制范围
            self.fine_tune_zone_var.set(value)
            self.save_fine_tune_zone_config()
        except ValueError:
            self.fine_tune_zone_display_var.set(f"{self.fine_tune_zone_var.get():.0f}")
            messagebox.showwarning("警告", "请输入有效的数值 (1-20)")

    # 新增参数的处理方法
    def update_speed_factor_display(self):
        """更新速度因子显示."""
        if hasattr(self, 'speed_factor_display_var'):
            self.speed_factor_display_var.set(f"{self.speed_factor_var.get():.2f}")

    def save_speed_factor_config(self):
        """保存速度因子配置."""
        try:
            value = self.speed_factor_var.get()
            self.update_config('movement', 'speed_factor', str(value))
            print(f"✅ 速度因子已保存: {value:.2f}")
        except Exception as e:
            print(f"❌ 速度因子保存失败: {e}")

    def update_speed_factor_from_entry(self):
        """从输入框更新速度因子."""
        try:
            value = float(self.speed_factor_display_var.get())
            value = max(0.1, min(1.0, value))
            self.speed_factor_var.set(value)
            self.save_speed_factor_config()
        except Exception as e:
            self.speed_factor_display_var.set(f"{self.speed_factor_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    def update_max_move_distance_display(self):
        """更新最大移动距离显示."""
        if hasattr(self, 'max_move_distance_display_var'):
            self.max_move_distance_display_var.set(f"{self.max_move_distance_var.get():.0f}")

    def save_max_move_distance_config(self):
        """保存最大移动距离配置."""
        try:
            value = self.max_move_distance_var.get()
            self.update_config('movement', 'max_move_distance', str(value))
            print(f"✅ 最大移动距离已保存: {value:.0f}")
        except Exception as e:
            print(f"❌ 最大移动距离保存失败: {e}")

    def update_max_move_distance_from_entry(self):
        """从输入框更新最大移动距离."""
        try:
            value = float(self.max_move_distance_display_var.get())
            value = max(10.0, min(127.0, value))
            self.max_move_distance_var.set(value)
            self.save_max_move_distance_config()
        except Exception as e:
            self.max_move_distance_display_var.set(f"{self.max_move_distance_var.get():.0f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    def update_movement_smoothing_steps_display(self):
        """更新移动平滑步数显示."""
        if hasattr(self, 'movement_smoothing_steps_display_var'):
            self.movement_smoothing_steps_display_var.set(f"{self.movement_smoothing_steps_var.get()}")

    def save_movement_smoothing_steps_config(self):
        """保存移动平滑步数配置."""
        try:
            value = self.movement_smoothing_steps_var.get()
            self.update_config('movement', 'movement_smoothing_steps', str(value))
            print(f"✅ 移动平滑步数已保存: {value}")
        except Exception as e:
            print(f"❌ 移动平滑步数保存失败: {e}")

    def update_movement_smoothing_steps_from_entry(self):
        """从输入框更新移动平滑步数."""
        try:
            value = int(self.movement_smoothing_steps_display_var.get())
            value = max(1, min(10, value))
            self.movement_smoothing_steps_var.set(value)
            self.save_movement_smoothing_steps_config()
        except Exception as e:
            self.movement_smoothing_steps_display_var.set(f"{self.movement_smoothing_steps_var.get()}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # X轴补偿相关方法
    def update_comp_x_display(self):
        """更新X轴补偿显示."""
        if self.comp_x_display_var:
            self.comp_x_display_var.set(f"{self.comp_x_var.get():.1f}")

    def save_comp_x_config(self):
        """保存X轴补偿配置."""
        self.update_config('compensation', 'compensation_x', f"{self.comp_x_var.get():.1f}")

    def update_comp_x_from_entry(self):
        """从输入框更新X轴补偿."""
        try:
            value = float(self.comp_x_display_var.get())
            if -50.0 <= value <= 50.0:
                self.comp_x_var.set(value)
                self.save_comp_x_config()
            else:
                self.comp_x_display_var.set(f"{self.comp_x_var.get():.1f}")
                messagebox.showwarning("警告", "X轴补偿值必须在-50.0到50.0之间")
        except ValueError:
            self.comp_x_display_var.set(f"{self.comp_x_var.get():.1f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # Y轴补偿相关方法
    def update_comp_y_display(self):
        """更新Y轴补偿显示."""
        if self.comp_y_display_var:
            self.comp_y_display_var.set(f"{self.comp_y_var.get():.1f}")

    def save_comp_y_config(self):
        """保存Y轴补偿配置."""
        self.update_config('compensation', 'compensation_y', f"{self.comp_y_var.get():.1f}")

    def update_comp_y_from_entry(self):
        """从输入框更新Y轴补偿."""
        try:
            value = float(self.comp_y_display_var.get())
            if -50.0 <= value <= 50.0:
                self.comp_y_var.set(value)
                self.save_comp_y_config()
            else:
                self.comp_y_display_var.set(f"{self.comp_y_var.get():.1f}")
                messagebox.showwarning("警告", "Y轴补偿值必须在-50.0到50.0之间")
        except ValueError:
            self.comp_y_display_var.set(f"{self.comp_y_var.get():.1f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # 恢复速率相关方法
    def update_recovery_rate_display(self):
        """更新恢复速率显示."""
        if self.recovery_rate_display_var:
            self.recovery_rate_display_var.set(f"{self.recovery_rate_var.get():.2f}")

    def save_recovery_rate_config(self):
        """保存恢复速率配置."""
        self.update_config('compensation', 'recovery_rate', f"{self.recovery_rate_var.get():.2f}")

    def update_recovery_rate_from_entry(self):
        """从输入框更新恢复速率."""
        try:
            value = float(self.recovery_rate_display_var.get())
            if 0.0 <= value <= 1.0:
                self.recovery_rate_var.set(value)
                self.save_recovery_rate_config()
            else:
                self.recovery_rate_display_var.set(f"{self.recovery_rate_var.get():.2f}")
                messagebox.showwarning("警告", "恢复速率必须在0.0-1.0之间")
        except ValueError:
            self.recovery_rate_display_var.set(f"{self.recovery_rate_var.get():.2f}")
            messagebox.showwarning("警告", "请输入有效的数值")

    # 配置文件管理方法
    def refresh_config_list(self):
        """刷新配置文件列表."""
        self.scan_config_profiles()
        self.config_listbox.delete(0, tk.END)

        for config in self.available_configs:
            self.config_listbox.insert(tk.END, config)

        # 高亮当前配置
        try:
            current_index = self.available_configs.index(self.current_config_name)
            self.config_listbox.selection_set(current_index)
            self.config_listbox.see(current_index)
        except ValueError:
            pass

        # 更新快速切换下拉菜单
        if hasattr(self, 'quick_config_var'):
            # 更新下拉菜单的值列表
            for widget in self.root.winfo_children():
                self._update_quick_combo_recursive(widget)
            self.quick_config_var.set(self.current_config_name)

    def load_selected_config(self):
        """加载选中的配置文件."""
        selection = self.config_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个配置文件")
            return

        selected_config = self.available_configs[selection[0]]

        # 检查是否是模板
        if selected_config.startswith("[模板]"):
            # 创建新的配置文件
            template_name = selected_config.replace("[模板] ", "")
            self.create_config_from_template(template_name)
            return

        # 确认对话框
        if messagebox.askyesno("确认", f"是否加载配置文件 '{selected_config}'？\n当前未保存的更改将丢失。"):
            self.load_config_file(selected_config)

    def load_config_file(self, config_name):
        """加载指定的配置文件."""
        try:
            if config_name == "settings.ini":
                config_path = self.config_file
            else:
                config_path = self.config_profiles_dir / config_name

            if not config_path.exists():
                messagebox.showerror("错误", f"配置文件 '{config_name}' 不存在")
                return

            # 备份当前配置
            self.backup_current_config()

            # 加载新配置
            self.config.clear()
            self.config.read(str(config_path), encoding='utf-8')

            # 更新当前配置名称
            self.current_config_name = config_name
            self.current_config_label.config(text=config_name)

            # 刷新GUI显示
            self.refresh_gui_values()

            # 保存到主配置文件
            self.save_config()

            self.status_var.set(f"已加载配置: {config_name}")
            print(f"✅ 已加载配置文件: {config_name}")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")
            print(f"❌ 加载配置失败: {e}")

    def save_config_as(self):
        """另存为配置文件."""
        # 获取保存名称
        config_name = simpledialog.askstring(
            "另存为配置",
            "请输入配置文件名称（不需要.ini扩展名）:",
            initialvalue="my_config"
        )

        if not config_name:
            return

        # 确保文件名有效
        config_name = config_name.strip()
        if not config_name.endswith('.ini'):
            config_name += '.ini'

        # 检查文件名是否有效
        if not self.is_valid_filename(config_name):
            messagebox.showerror("错误", "文件名包含无效字符")
            return

        try:
            # 保存到配置文件目录
            config_path = self.config_profiles_dir / config_name

            # 检查是否已存在
            if config_path.exists():
                if not messagebox.askyesno("确认", f"配置文件 '{config_name}' 已存在，是否覆盖？"):
                    return

            # 保存当前配置
            with open(str(config_path), 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 更新当前配置名称
            self.current_config_name = config_name
            self.current_config_label.config(text=config_name)

            # 刷新列表
            self.refresh_config_list()

            self.status_var.set(f"配置已保存为: {config_name}")
            print(f"✅ 配置已保存为: {config_name}")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
            print(f"❌ 保存配置失败: {e}")

    def import_config_file(self):
        """导入配置文件."""
        file_path = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("INI files", "*.ini"), ("All files", "*.*")],
            initialdir=str(self.project_root)
        )

        if not file_path:
            return

        try:
            # 验证配置文件
            test_config = configparser.ConfigParser()
            test_config.read(file_path, encoding='utf-8')

            # 获取文件名
            source_path = Path(file_path)
            config_name = source_path.name

            # 复制到配置目录
            target_path = self.config_profiles_dir / config_name

            if target_path.exists():
                if not messagebox.askyesno("确认", f"配置文件 '{config_name}' 已存在，是否覆盖？"):
                    return

            shutil.copy2(file_path, str(target_path))

            # 刷新列表
            self.refresh_config_list()

            # 询问是否立即加载
            if messagebox.askyesno("导入成功", f"配置文件 '{config_name}' 导入成功！\n是否立即加载此配置？"):
                self.load_config_file(config_name)

            self.status_var.set(f"已导入配置: {config_name}")
            print(f"✅ 已导入配置文件: {config_name}")

        except Exception as e:
            messagebox.showerror("错误", f"导入配置文件失败: {e}")
            print(f"❌ 导入配置失败: {e}")

    def rename_config(self):
        """重命名配置文件."""
        selection = self.config_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个配置文件")
            return

        old_name = self.available_configs[selection[0]]

        # 不能重命名默认配置和模板
        if old_name == "settings.ini" or old_name.startswith("[模板]"):
            messagebox.showwarning("警告", "无法重命名默认配置或模板")
            return

        # 获取新名称
        new_name = simpledialog.askstring(
            "重命名配置",
            f"请输入新的配置文件名称:",
            initialvalue=old_name.replace('.ini', '')
        )

        if not new_name:
            return

        new_name = new_name.strip()
        if not new_name.endswith('.ini'):
            new_name += '.ini'

        if not self.is_valid_filename(new_name):
            messagebox.showerror("错误", "文件名包含无效字符")
            return

        try:
            old_path = self.config_profiles_dir / old_name
            new_path = self.config_profiles_dir / new_name

            if new_path.exists():
                messagebox.showerror("错误", f"配置文件 '{new_name}' 已存在")
                return

            old_path.rename(new_path)

            # 如果重命名的是当前配置，更新当前配置名称
            if self.current_config_name == old_name:
                self.current_config_name = new_name
                self.current_config_label.config(text=new_name)

            self.refresh_config_list()
            self.status_var.set(f"配置已重命名: {old_name} -> {new_name}")
            print(f"✅ 配置已重命名: {old_name} -> {new_name}")

        except Exception as e:
            messagebox.showerror("错误", f"重命名失败: {e}")
            print(f"❌ 重命名失败: {e}")

    def delete_config(self):
        """删除配置文件."""
        selection = self.config_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个配置文件")
            return

        config_name = self.available_configs[selection[0]]

        # 不能删除默认配置和模板
        if config_name == "settings.ini" or config_name.startswith("[模板]"):
            messagebox.showwarning("警告", "无法删除默认配置或模板")
            return

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除配置文件 '{config_name}' 吗？\n此操作无法撤销。"):
            return

        try:
            config_path = self.config_profiles_dir / config_name
            config_path.unlink()

            # 如果删除的是当前配置，切换到默认配置
            if self.current_config_name == config_name:
                self.current_config_name = "settings.ini"
                self.current_config_label.config(text="settings.ini")

            self.refresh_config_list()
            self.status_var.set(f"已删除配置: {config_name}")
            print(f"✅ 已删除配置文件: {config_name}")

        except Exception as e:
            messagebox.showerror("错误", f"删除失败: {e}")
            print(f"❌ 删除失败: {e}")

    def create_config_from_template(self, template_name):
        """从模板创建新配置."""
        # 获取配置名称
        config_name = simpledialog.askstring(
            "创建配置",
            f"基于模板 '{template_name}' 创建新配置\n请输入配置文件名称:",
            initialvalue=template_name.replace('.ini', '')
        )

        if not config_name:
            return

        config_name = config_name.strip()
        if not config_name.endswith('.ini'):
            config_name += '.ini'

        try:
            # 创建基于当前配置的新配置文件
            config_path = self.config_profiles_dir / config_name

            if config_path.exists():
                if not messagebox.askyesno("确认", f"配置文件 '{config_name}' 已存在，是否覆盖？"):
                    return

            # 保存当前配置作为新配置
            with open(str(config_path), 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 更新当前配置名称
            self.current_config_name = config_name
            self.current_config_label.config(text=config_name)

            self.refresh_config_list()
            self.status_var.set(f"已创建配置: {config_name}")
            print(f"✅ 已创建配置文件: {config_name}")

        except Exception as e:
            messagebox.showerror("错误", f"创建配置失败: {e}")
            print(f"❌ 创建配置失败: {e}")

    def backup_current_config(self):
        """备份当前配置."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.ini"
            backup_path = self.config_profiles_dir / backup_name

            with open(str(backup_path), 'w', encoding='utf-8') as f:
                self.config.write(f)

            print(f"✅ 已备份当前配置: {backup_name}")

        except Exception as e:
            print(f"⚠️ 备份配置失败: {e}")

    def is_valid_filename(self, filename):
        """检查文件名是否有效."""
        import re
        # Windows文件名不能包含的字符
        invalid_chars = r'[<>:"/\\|?*]'
        return not re.search(invalid_chars, filename) and filename.strip() != ""

    def refresh_gui_values(self):
        """刷新GUI中所有控件的值."""
        try:
            # 基本设置
            if hasattr(self, 'detection_var'):
                self.detection_var.set(self.config.get('detection', 'detection_mode', fallback='color'))
            if hasattr(self, 'input_var'):
                # 尝试从新的[input]节读取，回退到[enhancement]节
                input_value = self.config.get('input', 'input_method', fallback=None)
                if input_value is None:
                    input_value = self.config.get('enhancement', 'input_method', fallback='winapi')
                self.input_var.set(input_value)
                self.update_hardware_config_visibility()
            if hasattr(self, 'debug_var'):
                self.debug_var.set(self.config.getboolean('debug', 'enabled', fallback=True))

            # 数值控件
            if hasattr(self, 'speed_var'):
                self.speed_var.set(float(self.config.get('enhancement', 'movement_speed', fallback='1.0')))
                self.update_speed_display()
            if hasattr(self, 'height_ratio_var'):
                self.height_ratio_var.set(float(self.config.get('enhancement', 'target_height_ratio', fallback='0.5')))
                self.update_height_ratio_display()
            if hasattr(self, 'smoothing_var'):
                self.smoothing_var.set(float(self.config.get('movement', 'movement_smoothing_factor', fallback='0.3')))
                self.update_smoothing_display()
            if hasattr(self, 'control_method_var'):
                self.control_method_var.set(self.config.get('movement', 'control_method', fallback='adaptive'))
                self.update_pid_config_visibility()
            if hasattr(self, 'kp_var'):
                self.kp_var.set(float(self.config.get('movement', 'pid_kp', fallback='0.8')))
                self.update_kp_display()
            if hasattr(self, 'ki_var'):
                self.ki_var.set(float(self.config.get('movement', 'pid_ki', fallback='0.1')))
                self.update_ki_display()
            if hasattr(self, 'kd_var'):
                self.kd_var.set(float(self.config.get('movement', 'pid_kd', fallback='0.2')))
                self.update_kd_display()
            if hasattr(self, 'pid_debug_var'):
                self.pid_debug_var.set(self.config.getboolean('movement', 'debug_pid', fallback=False))

            # 高级设置
            if hasattr(self, 'model_var'):
                self.model_var.set(self.config.get('visual_yolo', 'model_type', fallback='yolov8n'))
            if hasattr(self, 'conf_var'):
                self.conf_var.set(float(self.config.get('visual_yolo', 'confidence_threshold', fallback='0.5')))
                self.update_conf_display()
            if hasattr(self, 'nms_var'):
                self.nms_var.set(float(self.config.get('visual_yolo', 'nms_threshold', fallback='0.4')))
            if hasattr(self, 'region_var'):
                self.region_var.set(int(self.config.get('visual_common', 'capture_region_x', fallback='256')))
            if hasattr(self, 'anti_detect_var'):
                self.anti_detect_var.set(self.config.getboolean('visual_yolo', 'enable_anti_detection', fallback=True))
            if hasattr(self, 'batch_size_var'):
                self.batch_size_var.set(int(self.config.get('visual_yolo', 'batch_size', fallback='1')))
            if hasattr(self, 'model_format_var'):
                self.model_format_var.set(self.config.get('visual_yolo', 'model_format', fallback='auto'))
            if hasattr(self, 'tensorrt_var'):
                self.tensorrt_var.set(self.config.getboolean('visual_yolo', 'use_tensorrt', fallback=False))
            if hasattr(self, 'use_gpu_var'):
                self.use_gpu_var.set(self.config.getboolean('visual_yolo', 'use_gpu', fallback=True))

            # 目标类别设置
            if hasattr(self, 'target_classes_vars'):
                self.refresh_target_classes_from_config()

            print("✅ GUI值已刷新")

        except Exception as e:
            print(f"⚠️ 刷新GUI值时出错: {e}")

    # 移除控件分类和锁定机制，使用传统配置模式

    def on_quick_config_change(self, event=None):
        """快速配置切换事件处理."""
        selected_config = self.quick_config_var.get()
        if selected_config and selected_config != self.current_config_name:
            if messagebox.askyesno("确认切换", f"是否切换到配置 '{selected_config}'？\n当前未保存的更改将丢失。"):
                if selected_config.startswith("[模板]"):
                    template_name = selected_config.replace("[模板] ", "")
                    self.create_config_from_template(template_name)
                else:
                    self.load_config_file(selected_config)
            else:
                # 恢复原选择
                self.quick_config_var.set(self.current_config_name)

    # 移除进程控制方法，使用传统配置模式

    def _update_quick_combo_recursive(self, widget):
        """递归更新快速切换下拉菜单."""
        if isinstance(widget, ttk.Combobox) and hasattr(widget, 'cget'):
            try:
                if hasattr(widget, 'textvariable') and widget.cget('textvariable') == str(self.quick_config_var):
                    widget['values'] = self.available_configs
            except:
                pass

        # 递归处理子控件
        for child in widget.winfo_children():
            self._update_quick_combo_recursive(child)

    def refresh_models(self):
        """刷新模型列表."""
        print("🔄 刷新模型列表...")
        self.available_models = self.scan_available_models()

        # 更新下拉菜单
        for widget in self.root.winfo_children():
            self._update_model_combo_recursive(widget)

        self.status_var.set("模型列表已刷新")

        # 重新验证当前模型
        if hasattr(self, 'model_var'):
            self.validate_current_model()

    def on_model_changed(self):
        """模型选择改变时的回调."""
        model_name = self.model_var.get()
        self.update_config('visual_yolo', 'model_type', model_name)
        self.validate_current_model()

    def validate_current_model(self):
        """验证当前选择的模型."""
        if not hasattr(self, 'model_var') or not hasattr(self, 'model_status_var'):
            return

        model_name = self.model_var.get()
        if not model_name:
            self.model_status_var.set("未选择模型")
            return

        # 查找模型文件路径
        model_path = None
        search_dirs = [
            self.project_root / "models",
            self.project_root / "src" / "models"
        ]

        for search_dir in search_dirs:
            potential_path = search_dir / model_name
            if potential_path.exists():
                model_path = potential_path
                break

        if model_path:
            is_valid, status_msg = self.validate_model_file(str(model_path))
            color = self.get_model_status_color(is_valid)
            self.model_status_var.set(status_msg)
            if hasattr(self, 'model_status_label'):
                self.model_status_label.configure(foreground=color)
        else:
            self.model_status_var.set(f"文件未找到: {model_name}")
            if hasattr(self, 'model_status_label'):
                self.model_status_label.configure(foreground="red")

    def _update_model_combo_recursive(self, widget):
        """递归更新模型下拉菜单."""
        if isinstance(widget, ttk.Combobox) and hasattr(widget, 'cget'):
            try:
                if 'yolo' in str(widget.cget('values')).lower():
                    widget['values'] = self.available_models
            except:
                pass

        # 递归处理子控件
        for child in widget.winfo_children():
            self._update_model_combo_recursive(child)
            
    def reload_config(self):
        """Reload configuration."""
        self.load_config()

        # 重载智能移动算法参数
        if hasattr(self, 'velocity_scale_x_var'):
            self.velocity_scale_x_var.set(float(self.config.get('movement', 'velocity_scale_x', fallback='1.2')))
            self.update_velocity_scale_x_display()
        if hasattr(self, 'velocity_scale_y_var'):
            self.velocity_scale_y_var.set(float(self.config.get('movement', 'velocity_scale_y', fallback='1.0')))
            self.update_velocity_scale_y_display()
        if hasattr(self, 'dynamic_boost_var'):
            self.dynamic_boost_var.set(float(self.config.get('movement', 'dynamic_speed_boost', fallback='2.0')))
            self.update_dynamic_boost_display()
        if hasattr(self, 'deceleration_zone_var'):
            self.deceleration_zone_var.set(float(self.config.get('movement', 'deceleration_zone', fallback='15')))
            self.update_deceleration_zone_display()
        if hasattr(self, 'fine_tune_zone_var'):
            self.fine_tune_zone_var.set(float(self.config.get('movement', 'fine_tune_zone', fallback='5')))
            self.update_fine_tune_zone_display()

        self.status_var.set("配置已重载")
        print("✅ 配置已重载")
        
    def backup_config(self):
        """Backup configuration."""
        try:
            import shutil
            shutil.copy2(str(self.config_file), str(self.backup_file))
            self.status_var.set("配置已备份")
            print("✅ 配置已备份")
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            
    def restore_config(self):
        """Restore configuration."""
        try:
            if self.backup_file.exists():
                import shutil
                shutil.copy2(str(self.backup_file), str(self.config_file))
                self.reload_config()
                self.status_var.set("配置已恢复")
                print("✅ 配置已恢复")
            else:
                messagebox.showwarning("警告", "备份文件不存在")
        except Exception as e:
            print(f"❌ 恢复失败: {e}")

    # 🔧 延时压枪控制相关方法
    def update_delay_compensation_display(self):
        """更新延时时间显示."""
        if hasattr(self, 'delay_display_var'):
            self.delay_display_var.set(f"{int(self.delay_compensation_var.get())}")

    def save_delay_compensation_config(self):
        """保存延时时间配置."""
        self.update_config('compensation', 'delay_before_compensation', f"{int(self.delay_compensation_var.get())}")

    def update_delay_compensation_from_entry(self):
        """从输入框更新延时时间."""
        try:
            value = int(self.delay_display_var.get())
            value = max(0, min(2000, value))  # 限制范围
            self.delay_compensation_var.set(value)
            self.delay_display_var.set(str(value))
            self.save_delay_compensation_config()
        except ValueError:
            # 输入无效时恢复原值
            self.delay_display_var.set(f"{int(self.delay_compensation_var.get())}")

    def update_enhanced_multiplier_display(self):
        """更新增强倍数显示."""
        if hasattr(self, 'multiplier_display_var'):
            self.multiplier_display_var.set(f"{self.enhanced_multiplier_var.get():.1f}")

    def save_enhanced_multiplier_config(self):
        """保存增强倍数配置."""
        self.update_config('compensation', 'enhanced_compensation_multiplier', f"{self.enhanced_multiplier_var.get():.1f}")

    def update_enhanced_multiplier_from_entry(self):
        """从输入框更新增强倍数."""
        try:
            value = float(self.multiplier_display_var.get())
            value = max(1.0, min(10.0, value))  # 限制范围
            self.enhanced_multiplier_var.set(value)
            self.multiplier_display_var.set(f"{value:.1f}")
            self.save_enhanced_multiplier_config()
        except ValueError:
            # 输入无效时恢复原值
            self.multiplier_display_var.set(f"{self.enhanced_multiplier_var.get():.1f}")

    def update_light_compensation_display(self):
        """更新轻微补偿系数显示."""
        if hasattr(self, 'light_display_var'):
            self.light_display_var.set(f"{self.light_compensation_var.get():.2f}")

    def save_light_compensation_config(self):
        """保存轻微补偿系数配置."""
        self.update_config('compensation', 'light_compensation_factor', f"{self.light_compensation_var.get():.2f}")

    def update_light_compensation_from_entry(self):
        """从输入框更新轻微补偿系数."""
        try:
            value = float(self.light_display_var.get())
            value = max(0.0, min(1.0, value))  # 限制范围0.0-1.0
            self.light_compensation_var.set(value)
            self.light_display_var.set(f"{value:.2f}")
            self.save_light_compensation_config()
        except ValueError:
            # 输入无效时恢复原值
            self.light_display_var.set(f"{self.light_compensation_var.get():.2f}")

    def restart_main_program(self):
        """重启主程序."""
        try:
            import subprocess
            import sys
            from tkinter import messagebox

            # 确认对话框
            result = messagebox.askyesno(
                "重启程序",
                "这将重启主程序以应用需要重启的配置更改。\n\n"
                "确定要继续吗？",
                icon="question"
            )

            if result:
                # 保存当前配置
                self.save_config()

                # 显示提示信息
                messagebox.showinfo(
                    "重启中",
                    "程序将在3秒后重启...\n\n"
                    "请等待主程序重新启动。",
                    icon="info"
                )

                # 启动新的主程序实例
                main_script = self.project_root / "start.py"
                if main_script.exists():
                    subprocess.Popen([sys.executable, str(main_script)],
                                   cwd=str(self.project_root))
                    print("✅ 主程序重启命令已发送")
                    self.status_var.set("主程序重启中...")
                else:
                    messagebox.showerror("错误", f"找不到主程序文件: {main_script}")

        except Exception as e:
            print(f"❌ 重启程序失败: {e}")
            messagebox.showerror("错误", f"重启程序失败: {e}")

    def run(self):
        """Run the GUI."""
        print("🚀 启动增强配置GUI...")

        # 创建GUI界面
        self.create_gui()
        self.create_main_interface()

        # 延迟刷新GUI值以显示配置文件中的数据
        self.root.after(500, self.refresh_gui_values)

        # 移除自动应用控件状态管理 - 只有在用户手动点击按钮时才应用
        # self.root.after(1000, self.apply_widget_states_after_startup)

        # 延迟验证当前模型
        self.root.after(1500, self.validate_current_model)

        self.root.mainloop()
        print("✅ GUI已退出")

def main():
    """Main function."""
    print("=" * 50)
    print("    Unibot 简单配置工具")
    print("=" * 50)
    
    try:
        app = SimpleConfigGUI()
        app.run()
    except Exception as e:
        print(f"❌ 程序失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
