"""
    YOLO Target Detector - Advanced neural network based target identification
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import cv2
import numpy as np
import time
import random
import sys
import os
from typing import List, Tuple, Optional, Dict, Any
from pathlib import Path

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
try:
    from utils.performance_utils import ThreadSafeLRUCache, EfficientFrameHasher, PerformanceMonitor, UnifiedExceptionHandler
    PERFORMANCE_UTILS_AVAILABLE = True
except ImportError:
    PERFORMANCE_UTILS_AVAILABLE = False

try:
    import torch
    import torchvision.transforms as transforms
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("Warning: PyTorch not available. YOLO detection will be disabled.")

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    print("Warning: Ultralytics not available. Using fallback implementation.")

try:
    from .optimized_model_manager import OptimizedModelManager
    OPTIMIZED_MANAGER_AVAILABLE = True
except ImportError:
    OPTIMIZED_MANAGER_AVAILABLE = False


class YOLODetector:
    """Advanced neural network based target identification system."""
    
    def __init__(self, config):
        """Initialize the YOLO detector."""
        self.config = config
        self.model = None
        self.device = 'cpu'
        self.is_initialized = False

        # Performance optimization with new utils
        self.last_inference_time = 0
        # 使用统一FPS配置，避免硬编码
        target_fps = self._get_unified_fps_config(config)
        self.min_inference_interval = 1.0 / target_fps

        # 使用优化后的缓存系统
        if PERFORMANCE_UTILS_AVAILABLE:
            cache_size = getattr(config, 'cache_size', 50)
            cache_ttl = getattr(config, 'cache_ttl', 0.1)
            self.inference_cache = ThreadSafeLRUCache(max_size=cache_size, ttl=cache_ttl)
            self.frame_hasher = EfficientFrameHasher()
            self.performance_monitor = PerformanceMonitor()
            self.exception_handler = UnifiedExceptionHandler("yolo_detector")
        else:
            # 回退到原始缓存
            self.inference_cache = {}
            self.cache_timeout = 0.1

        # Target filtering
        self.target_classes = getattr(config, 'target_classes', [0])  # Default: person
        self.confidence_threshold = getattr(config, 'confidence_threshold', 0.5)
        self.nms_threshold = getattr(config, 'nms_threshold', 0.4)

        # Initialize optimized model manager
        self.optimized_manager = None
        if OPTIMIZED_MANAGER_AVAILABLE:
            cache_size = getattr(config, 'model_cache_size', 3)
            self.optimized_manager = OptimizedModelManager(config, cache_size)

        # Initialize if dependencies available
        if TORCH_AVAILABLE and ULTRALYTICS_AVAILABLE:
            self._initialize_model()
        else:
            self.is_initialized = False

    def _get_unified_fps_config(self, config) -> float:
        """获取统一的FPS配置，避免硬编码"""
        try:
            # 1. 检查YOLO特定FPS设置
            if hasattr(config, 'parser') and config.parser.has_section('visual_yolo'):
                try:
                    yolo_fps = float(config.parser.get('visual_yolo', 'max_fps', fallback=None))
                    if yolo_fps and yolo_fps > 0:
                        print(f"🎯 YOLO检测器使用特定FPS: {yolo_fps}")
                        return yolo_fps
                except (ValueError, TypeError):
                    pass

            # 2. 使用全局performance节的max_fps
            global_fps = getattr(config, 'max_fps', None)
            if global_fps and global_fps > 0:
                print(f"🎯 YOLO检测器使用全局FPS: {global_fps}")
                return float(global_fps)

            # 3. 从配置文件直接读取
            if hasattr(config, 'parser') and config.parser.has_section('performance'):
                try:
                    config_fps = float(config.parser.get('performance', 'max_fps', fallback=None))
                    if config_fps and config_fps > 0:
                        print(f"🎯 YOLO检测器从配置文件读取FPS: {config_fps}")
                        return config_fps
                except (ValueError, TypeError):
                    pass

            # 4. 动态默认值
            try:
                import psutil
                cpu_count = psutil.cpu_count()
                if cpu_count >= 8:
                    default_fps = 120.0
                elif cpu_count >= 4:
                    default_fps = 90.0
                else:
                    default_fps = 60.0
                print(f"🎯 YOLO检测器使用动态默认FPS: {default_fps}")
                return default_fps
            except ImportError:
                return 60.0

        except Exception as e:
            print(f"⚠️ YOLO检测器FPS配置读取失败: {e}")
            return 60.0

    def _initialize_model(self):
        """Initialize the YOLO model with support for both PT and ONNX formats."""
        try:
            # Add anti-detection randomization
            import random
            import time
            time.sleep(random.uniform(0.02, 0.08))  # Random initialization delay

            # Determine device
            if (hasattr(self.config, 'use_gpu') and self.config.use_gpu and
                TORCH_AVAILABLE and torch and torch.cuda.is_available()):
                self.device = 'cuda'
            else:
                self.device = 'cpu'

            # Get model configuration
            model_type = getattr(self.config, 'model_type', 'yolov8n')
            model_format = getattr(self.config, 'model_format', 'auto')  # 'auto', 'pt', 'onnx'
            custom_model_path = getattr(self.config, 'model_path', None)

            model_path = None

            # Priority 1: Use custom model path if specified
            if custom_model_path and Path(custom_model_path).exists():
                model_path = Path(custom_model_path)

            # Priority 2: Direct model file lookup (for specific models like CS2.onnx)
            if not model_path:
                models_dir = Path("models")
                # Try the exact model_type first (handles cases like CS2.onnx)
                direct_path = models_dir / model_type
                if direct_path.exists():
                    model_path = direct_path
                else:
                    # Try with common extensions
                    potential_paths = [
                        models_dir / f"{model_type}.onnx",
                        models_dir / f"{model_type}.pt"
                    ]
                    for path in potential_paths:
                        if path.exists():
                            model_path = path
                            break

            # Priority 3: Use model manager only if direct lookup failed and model_type is a standard YOLO variant
            if not model_path and model_type.startswith(('yolo', 'yv')):
                try:
                    from .model_manager import ModelManager
                    model_manager = ModelManager(self.config)
                    model_path = model_manager.get_model_path(model_type, model_format, self.config)
                except ImportError:
                    pass  # Model manager not available

            if not model_path or not model_path.exists():
                raise FileNotFoundError(f"Model file not found. Tried: {model_type}")

            # Load model with optimized manager or fallback to direct loading
            # 🔧 获取模型输入尺寸
            input_size = self._get_model_input_size(model_path)
            imgsz = input_size[0] if input_size != (640, 640) else 640

            # Use optimized model manager if available
            if self.optimized_manager:

                def progress_callback(path, success, load_time):
                    pass  # Progress callback removed

                print(f"🔧 使用优化管理器加载模型，输入尺寸: {imgsz}x{imgsz}")
                # 注意：优化管理器可能不支持imgsz参数，这里先尝试标准加载
                self.model = self.optimized_manager.get_model_with_progress(
                    str(model_path), progress_callback
                )

                if not self.model:
                    raise RuntimeError("优化模型管理器加载失败")

                # 🔧 强制设置优化管理器加载的模型输入尺寸
                if hasattr(self.model, 'predictor') and self.model.predictor:
                    self.model.predictor.args.imgsz = imgsz
                    self.model.predictor.imgsz = imgsz
                    print(f"✅ 优化管理器模型已设置predictor.args.imgsz = {imgsz}")
                    print(f"✅ 优化管理器模型已设置predictor.imgsz = {imgsz}")

                if hasattr(self.model, 'args'):
                    self.model.args.imgsz = imgsz
                    print(f"✅ 优化管理器模型已设置model.args.imgsz = {imgsz}")

                # 🔧 关键修复：直接设置模型的imgsz属性
                if hasattr(self.model, 'imgsz'):
                    self.model.imgsz = imgsz
                    print(f"✅ 优化管理器模型已设置model.imgsz = {imgsz}")
                else:
                    self.model.imgsz = imgsz
                    print(f"✅ 优化管理器模型已创建并设置model.imgsz = {imgsz}")

            else:
                # Fallback to direct loading
                # Add randomization to avoid detection patterns
                time.sleep(random.uniform(0.01, 0.03))

                # 🔧 获取模型输入尺寸并初始化
                input_size = self._get_model_input_size(model_path)
                imgsz = input_size[0] if input_size != (640, 640) else 640

                print(f"🔧 使用输入尺寸初始化YOLO模型: {imgsz}x{imgsz}")
                self.model = YOLO(str(model_path))

                # 🔧 强制设置模型的输入尺寸
                if hasattr(self.model, 'predictor') and self.model.predictor:
                    self.model.predictor.args.imgsz = imgsz
                    self.model.predictor.imgsz = imgsz  # 直接设置imgsz属性
                    print(f"✅ 已设置predictor.args.imgsz = {imgsz}")
                    print(f"✅ 已设置predictor.imgsz = {imgsz}")

                # 设置模型args
                if hasattr(self.model, 'args'):
                    self.model.args.imgsz = imgsz
                    print(f"✅ 已设置model.args.imgsz = {imgsz}")

                # 🔧 关键修复：直接设置模型的imgsz属性
                if hasattr(self.model, 'imgsz'):
                    self.model.imgsz = imgsz
                    print(f"✅ 已设置model.imgsz = {imgsz}")
                else:
                    # 如果没有imgsz属性，创建它
                    self.model.imgsz = imgsz
                    print(f"✅ 已创建并设置model.imgsz = {imgsz}")

            self.model_format = model_path.suffix.lower().replace('.', '')

            # Move model to device (for PT models)
            if hasattr(self.model, 'to') and self.model_format == 'pt':
                self.model.to(self.device)

            # 🔧 设置模型输入尺寸（不自动调整捕获区域）
            self._set_model_input_size(model_path)

            # Store model info for anti-detection
            self.model_info = {
                'path': str(model_path),
                'format': self.model_format,
                'type': model_type,
                'device': self.device
            }

            self.is_initialized = True

        except Exception as e:
            self.is_initialized = False

    # 已移除自动适配功能 - 保持用户设定的固定捕获区域

    def _get_model_input_size(self, model_path: Path) -> tuple:
        """获取模型的输入尺寸"""
        try:
            if model_path.suffix.lower() == '.onnx':
                return self._get_onnx_input_size(model_path)
            elif model_path.suffix.lower() in ['.pt', '.engine']:
                return self._get_tensorrt_input_size(model_path)
            else:
                return (640, 640)  # 默认尺寸
        except Exception:
            return (640, 640)

    def _get_onnx_input_size(self, onnx_path: Path) -> tuple:
        """获取ONNX模型的输入尺寸"""
        try:
            import onnx
            model = onnx.load(str(onnx_path))

            # 获取第一个输入的形状
            input_shape = model.graph.input[0].type.tensor_type.shape.dim
            if len(input_shape) >= 4:
                height = input_shape[2].dim_value
                width = input_shape[3].dim_value
                if height > 0 and width > 0:
                    return (height, width)
            return (640, 640)
        except Exception:
            return (640, 640)

    def _get_tensorrt_input_size(self, model_path: Path) -> tuple:
        """获取TensorRT模型的输入尺寸"""
        try:
            # 对于TensorRT引擎，尝试从文件名推断尺寸
            filename = model_path.stem.lower()

            # 特殊模型名称映射
            special_models = {
                '三角洲': (256, 256),
                'delta': (256, 256),
                'triangle': (256, 256),
            }

            # 检查特殊模型名称
            for model_name, size_tuple in special_models.items():
                if model_name in filename:
                    print(f"🎯 识别到特殊模型: {model_name}, 输入尺寸: {size_tuple}")
                    return size_tuple

            # 常见的尺寸模式
            size_patterns = {
                '256': (256, 256),
                '320': (320, 320),
                '416': (416, 416),
                '512': (512, 512),
                '640': (640, 640),
                '800': (800, 800),
                '1024': (1024, 1024),
                '1280': (1280, 1280)
            }

            for size_str, size_tuple in size_patterns.items():
                if size_str in filename:
                    return size_tuple

            # 如果没有找到特定尺寸，返回默认值
            return (640, 640)

        except Exception:
            return (640, 640)

    def _set_model_input_size(self, model_path: Path):
        """设置模型的输入尺寸"""
        try:
            input_size = self._get_model_input_size(model_path)

            if input_size and input_size != (640, 640):
                imgsz = input_size[0]
                print(f"🔧 强制设置模型输入尺寸: {imgsz}x{imgsz}")

                # 方法1: 更新模型的args
                if hasattr(self.model, 'args'):
                    self.model.args.imgsz = imgsz
                    print(f"✅ 已设置 model.args.imgsz = {imgsz}")

                # 方法2: 更新内部模型的args
                if hasattr(self.model, 'model') and hasattr(self.model.model, 'args'):
                    self.model.model.args.imgsz = imgsz
                    print(f"✅ 已设置 model.model.args.imgsz = {imgsz}")

                # 方法3: 更新预测器的输入尺寸
                if hasattr(self.model, 'predictor'):
                    if hasattr(self.model.predictor, 'imgsz'):
                        self.model.predictor.imgsz = imgsz
                        print(f"✅ 已设置 predictor.imgsz = {imgsz}")
                    if hasattr(self.model.predictor, 'args'):
                        self.model.predictor.args.imgsz = imgsz
                        print(f"✅ 已设置 predictor.args.imgsz = {imgsz}")

                # 方法4: 直接设置模型的imgsz属性
                if hasattr(self.model, 'imgsz'):
                    self.model.imgsz = imgsz
                    print(f"✅ 已设置 model.imgsz = {imgsz}")

                # 方法5: 重新初始化预测器（如果存在）
                try:
                    if hasattr(self.model, 'predictor') and self.model.predictor:
                        # 强制重新初始化预测器
                        self.model.predictor = None
                        # 下次调用predict时会自动重新初始化
                        print(f"✅ 已重置预测器，将使用新的输入尺寸")
                except Exception as e:
                    print(f"⚠️ 重置预测器失败: {e}")

        except Exception as e:
            print(f"⚠️ 设置模型输入尺寸失败: {e}")

    def preload_models_async(self):
        """异步预加载常用模型."""
        if self.optimized_manager:
            self.optimized_manager.preload_common_models()

    def benchmark_loading_performance(self, model_path: str = None) -> dict:
        """基准测试模型加载性能."""
        if not self.optimized_manager:
            return {'error': 'Optimized model manager not available'}

        if not model_path:
            # 使用当前模型路径
            model_path = getattr(self, 'model_info', {}).get('path')
            if not model_path:
                return {'error': 'No model path specified'}

        return self.optimized_manager.benchmark_model_loading(model_path)

    def get_loading_recommendations(self, model_path: str = None) -> dict:
        """获取模型加载优化建议."""
        if not self.optimized_manager:
            return {'error': 'Optimized model manager not available'}

        if not model_path:
            model_path = getattr(self, 'model_info', {}).get('path')
            if not model_path:
                return {'error': 'No model path specified'}

        return self.optimized_manager.get_loading_recommendations(model_path)

    def get_cache_info(self) -> dict:
        """获取模型缓存信息."""
        if self.optimized_manager:
            return self.optimized_manager.cache.get_cache_info()
        else:
            return {'error': 'Optimized model manager not available'}

    def clear_model_cache(self):
        """清空模型缓存."""
        if self.optimized_manager:
            self.optimized_manager.cache.clear_cache()
            print("🗑️ 模型缓存已清空")
        else:
            print("⚠️ 优化模型管理器不可用")
            
    def detect_targets(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Detect targets in the given frame with optimized performance."""
        if not self.is_initialized or self.model is None:
            return self._fallback_detection(frame)

        start_time = time.time()

        try:
            # 智能帧率控制
            current_time = time.time()
            if current_time - self.last_inference_time < self.min_inference_interval:
                return []  # 跳过此帧以维持目标FPS

            # 使用优化后的缓存系统
            if PERFORMANCE_UTILS_AVAILABLE:
                # 高效哈希计算
                frame_hash = self.frame_hasher.compute_hash(frame)

                # 检查缓存
                cached_result = self.inference_cache.get(frame_hash)
                if cached_result is not None:
                    return cached_result
            else:
                # 回退到原始缓存逻辑
                frame_hash = hash(frame.tobytes())
                cache_hit_chance = random.uniform(0.7, 1.0)

                if (frame_hash in self.inference_cache and
                    current_time - self.inference_cache[frame_hash]['timestamp'] < self.cache_timeout * cache_hit_chance):
                    time.sleep(random.uniform(0.001, 0.005))
                    return self.inference_cache[frame_hash]['results']

            # 优化后的推理参数
            inference_params = {
                'verbose': False,
                'conf': self.confidence_threshold,
                'iou': self.nms_threshold
            }

            # 执行推理
            results = self.model(frame, **inference_params)

            # 处理结果
            detections = self._process_results(results)

            # 缓存结果
            if PERFORMANCE_UTILS_AVAILABLE:
                self.inference_cache.put(frame_hash, detections)
            else:
                # 回退缓存逻辑
                if random.random() > 0.1:
                    self.inference_cache[frame_hash] = {
                        'results': detections,
                        'timestamp': current_time
                    }

                # 定期清理旧缓存
                if random.random() < 0.1:
                    self._clean_cache(current_time)

            self.last_inference_time = current_time
            return detections

        except Exception as e:
            if PERFORMANCE_UTILS_AVAILABLE:
                return self.exception_handler.handle_exception(e, "YOLO detection") or []
            else:
                return self._fallback_detection(frame)
        finally:
            # 记录性能数据
            if PERFORMANCE_UTILS_AVAILABLE:
                frame_time = time.time() - start_time
                self.performance_monitor.record_frame_time(frame_time)

                # 定期更新内存使用情况
                if random.random() < 0.1:  # 10%的概率更新内存监控
                    self.performance_monitor.update_memory_usage()

                    # 检查是否需要垃圾回收
                    if self.performance_monitor.should_trigger_gc():
                        from utils.performance_utils import force_garbage_collection
                        force_garbage_collection()
            
    def _process_results(self, results) -> List[Dict[str, Any]]:
        """Process YOLO inference results."""
        detections = []
        
        for result in results:
            if hasattr(result, 'boxes') and result.boxes is not None:
                boxes = result.boxes
                
                for i in range(len(boxes)):
                    # Extract box information
                    box = boxes.xyxy[i].cpu().numpy()  # x1, y1, x2, y2
                    confidence = float(boxes.conf[i].cpu().numpy())
                    class_id = int(boxes.cls[i].cpu().numpy())
                    
                    # Filter by confidence and class
                    if (confidence >= self.confidence_threshold and 
                        class_id in self.target_classes):
                        
                        # Calculate center and dimensions
                        x1, y1, x2, y2 = box
                        center_x = (x1 + x2) / 2
                        center_y = (y1 + y2) / 2
                        width = x2 - x1
                        height = y2 - y1
                        
                        detection = {
                            'center_x': center_x,
                            'center_y': center_y,
                            'width': width,
                            'height': height,
                            'confidence': confidence,
                            'class_id': class_id,
                            'bbox': [x1, y1, x2, y2]
                        }
                        
                        detections.append(detection)
        
        # Apply NMS if multiple detections
        if len(detections) > 1:
            detections = self._apply_nms(detections)
        
        # Sort by confidence
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        return detections
        
    def _apply_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply Non-Maximum Suppression to remove overlapping detections."""
        if len(detections) <= 1:
            return detections
        
        # Convert to format suitable for NMS
        boxes = np.array([det['bbox'] for det in detections])
        scores = np.array([det['confidence'] for det in detections])
        
        # Simple NMS implementation
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), 
            scores.tolist(), 
            self.confidence_threshold, 
            self.nms_threshold
        )
        
        if len(indices) > 0:
            indices = indices.flatten()
            return [detections[i] for i in indices]
        
        return detections
        
    def _fallback_detection(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Fallback detection method when YOLO is not available."""
        # Simple contour-based detection as fallback
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 500:  # Minimum area threshold
                x, y, w, h = cv2.boundingRect(contour)
                
                detection = {
                    'center_x': x + w / 2,
                    'center_y': y + h / 2,
                    'width': w,
                    'height': h,
                    'confidence': 0.5,  # Default confidence for fallback
                    'class_id': 0,  # Default class
                    'bbox': [x, y, x + w, y + h]
                }
                detections.append(detection)
        
        return detections[:5]  # Limit to top 5 detections

    def _randomize_detections(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply subtle randomization to detection results for anti-detection."""
        if not detections:
            return detections

        randomized = []
        for detection in detections:
            # Create a copy to avoid modifying original
            det = detection.copy()

            # Add very small random offsets to coordinates (sub-pixel level)
            # This simulates natural variation in detection algorithms
            offset_range = 0.5  # Maximum 0.5 pixel offset

            det['center_x'] += random.uniform(-offset_range, offset_range)
            det['center_y'] += random.uniform(-offset_range, offset_range)

            # Slightly randomize confidence (within 1% range)
            confidence_variation = 0.01
            det['confidence'] *= random.uniform(1 - confidence_variation, 1 + confidence_variation)
            det['confidence'] = max(0.1, min(1.0, det['confidence']))  # Keep within valid range

            # Update bbox with new center coordinates
            if 'bbox' in det:
                x1, y1, x2, y2 = det['bbox']
                width = x2 - x1
                height = y2 - y1

                new_x1 = det['center_x'] - width / 2
                new_y1 = det['center_y'] - height / 2
                new_x2 = det['center_x'] + width / 2
                new_y2 = det['center_y'] + height / 2

                det['bbox'] = [new_x1, new_y1, new_x2, new_y2]

            randomized.append(det)

        return randomized

    def _clean_cache(self, current_time: float):
        """Clean expired cache entries."""
        expired_keys = [
            key for key, value in self.inference_cache.items()
            if current_time - value['timestamp'] > self.cache_timeout * 2
        ]
        
        for key in expired_keys:
            del self.inference_cache[key]
            
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if not self.is_initialized:
            return {'status': 'not_initialized'}

        info = {
            'status': 'initialized',
            'device': self.device,
            'model_type': getattr(self.config, 'model_type', 'unknown'),
            'confidence_threshold': self.confidence_threshold,
            'target_classes': self.target_classes,
            'nms_threshold': self.nms_threshold
        }

        # Add model-specific information if available
        if hasattr(self, 'model_info'):
            info.update({
                'model_path': self.model_info.get('path'),
                'model_format': self.model_info.get('format'),
                'model_version': self.model_info.get('type')
            })

        # Add performance information
        if hasattr(self, 'last_inference_time'):
            info['last_inference_time'] = self.last_inference_time

        info['cache_size'] = len(self.inference_cache)
        info['cache_timeout'] = self.cache_timeout

        return info
        
    def update_config(self, new_config):
        """Update detector configuration."""
        self.config = new_config
        self.target_classes = getattr(new_config, 'target_classes', [0])
        self.confidence_threshold = getattr(new_config, 'confidence_threshold', 0.5)
        self.nms_threshold = getattr(new_config, 'nms_threshold', 0.4)

        # 更新性能参数，使用统一FPS配置避免硬编码
        target_fps = self._get_unified_fps_config(new_config)
        self.min_inference_interval = 1.0 / target_fps

        # 更新缓存设置
        if PERFORMANCE_UTILS_AVAILABLE and hasattr(self, 'inference_cache'):
            cache_size = getattr(new_config, 'cache_size', 50)
            cache_ttl = getattr(new_config, 'cache_ttl', 0.1)
            # 重新创建缓存以应用新设置
            self.inference_cache = ThreadSafeLRUCache(max_size=cache_size, ttl=cache_ttl)

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not PERFORMANCE_UTILS_AVAILABLE:
            return {'error': 'Performance utils not available'}

        stats = {}

        # 缓存统计
        if hasattr(self, 'inference_cache'):
            stats['cache'] = self.inference_cache.get_stats()

        # 性能监控统计
        if hasattr(self, 'performance_monitor'):
            stats['performance'] = self.performance_monitor.get_stats()

        # 错误统计
        if hasattr(self, 'exception_handler'):
            stats['errors'] = self.exception_handler.get_error_stats()

        return stats
        
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'model') and self.model is not None:
            del self.model
        if hasattr(self, 'inference_cache'):
            self.inference_cache.clear()
