"""
    Unified Visual Processor - Multi-mode target identification system
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import cv2
import mss
import numpy as np
import win32api
import time
import random
import sys
import os
from typing import Optional, Tuple, Dict, Any
from collections import deque
from behavior_randomizer import BehaviorRandomizer
ADVANCED_OPTIMIZER_AVAILABLE = False


# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
try:
    from utils.base_configurable import PerformanceConfigurable
    from utils.performance_utils import PerformanceMonitor, UnifiedExceptionHandler
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False

# Import both detection systems
from visual_processor import VisualProcessor as ColorVisualProcessor
try:
    from yolo.yolo_detector import YOLODetector
    from yolo.model_manager import ModelManager
    from yolo.detection_processor import DetectionProcessor
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("YOLO modules not available. Using color detection only.")


def get_unified_fps_config(config, detection_mode: str = None) -> float:
    """
    统一的FPS配置读取函数，避免硬编码

    优先级:
    1. [visual_yolo] max_fps (仅在YOLO模式下)
    2. [performance] max_fps (全局设置)
    3. 动态默认值 (基于系统能力)

    Args:
        config: 配置对象
        detection_mode: 检测模式 ('yolo', 'color', etc.)

    Returns:
        float: 目标FPS值
    """
    try:
        # 1. 检查YOLO特定FPS设置 (仅在YOLO模式下)
        if detection_mode == 'yolo':
            # 检查visual_yolo节中的max_fps
            yolo_fps = getattr(config, 'yolo_max_fps', None)
            if yolo_fps is None:
                # 尝试从配置中读取visual_yolo.max_fps
                if hasattr(config, 'parser') and config.parser.has_section('visual_yolo'):
                    try:
                        yolo_fps = float(config.parser.get('visual_yolo', 'max_fps', fallback=None))
                    except (ValueError, TypeError):
                        yolo_fps = None

            if yolo_fps and yolo_fps > 0:
                print(f"🎯 使用YOLO特定FPS: {yolo_fps}")
                return float(yolo_fps)

        # 2. 使用全局performance节的max_fps
        global_fps = getattr(config, 'max_fps', None)
        if global_fps and global_fps > 0:
            print(f"🎯 使用全局FPS设置: {global_fps}")
            return float(global_fps)

        # 3. 从配置文件直接读取
        if hasattr(config, 'parser') and config.parser.has_section('performance'):
            try:
                config_fps = float(config.parser.get('performance', 'max_fps', fallback=None))
                if config_fps and config_fps > 0:
                    print(f"🎯 从配置文件读取FPS: {config_fps}")
                    return config_fps
            except (ValueError, TypeError):
                pass

        # 4. 动态默认值 (基于系统能力，避免硬编码)
        try:
            import psutil
            cpu_count = psutil.cpu_count()
            if cpu_count >= 8:
                default_fps = 120.0  # 高性能系统
            elif cpu_count >= 4:
                default_fps = 90.0   # 中等性能系统
            else:
                default_fps = 60.0   # 低性能系统

            print(f"🎯 使用动态默认FPS: {default_fps} (基于{cpu_count}核CPU)")
            return default_fps
        except ImportError:
            # psutil不可用时的回退
            return 60.0

    except Exception as e:
        print(f"⚠️ FPS配置读取失败: {e}")
        # 最后的回退值
        return 60.0


class FPSCalculator:
    """FPS计算器，用于实时监控帧率"""

    def __init__(self, window_size: int = 30, target_fps: float = None):
        """
        初始化FPS计算器

        Args:
            window_size: 用于计算平均FPS的时间窗口大小（帧数）
            target_fps: 目标FPS，如果为None则使用动态默认值
        """
        self.window_size = window_size
        self.frame_times = deque(maxlen=window_size)
        self.last_frame_time = time.time()
        self.current_fps = 0.0
        # 使用传入的target_fps或动态默认值，避免硬编码
        self.target_fps = target_fps if target_fps is not None else self._get_dynamic_default_fps()

    def _get_dynamic_default_fps(self) -> float:
        """获取动态默认FPS，避免硬编码"""
        try:
            import psutil
            cpu_count = psutil.cpu_count()
            if cpu_count >= 8:
                return 120.0  # 高性能系统
            elif cpu_count >= 4:
                return 90.0   # 中等性能系统
            else:
                return 60.0   # 低性能系统
        except ImportError:
            return 60.0  # psutil不可用时的回退

    def update(self) -> float:
        """
        更新FPS计算

        Returns:
            当前FPS值
        """
        current_time = time.time()
        frame_time = current_time - self.last_frame_time
        self.last_frame_time = current_time

        self.frame_times.append(frame_time)

        if len(self.frame_times) > 1:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            self.current_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0.0

        return self.current_fps

    def get_fps(self) -> float:
        """获取当前FPS"""
        return self.current_fps

    def set_target_fps(self, target_fps: float):
        """设置目标FPS"""
        self.target_fps = target_fps

    def get_target_fps(self) -> float:
        """获取目标FPS"""
        return self.target_fps

    def get_fps_info(self) -> Dict[str, float]:
        """获取FPS信息"""
        return {
            'current_fps': self.current_fps,
            'target_fps': self.target_fps,
            'efficiency': (self.current_fps / self.target_fps * 100) if self.target_fps > 0 else 0
        }


class UnifiedVisualProcessor(PerformanceConfigurable if UTILS_AVAILABLE else object):
    """Unified visual processing system supporting multiple detection modes."""

    def __init__(self, config):
        """Initialize the unified visual processor."""
        # 初始化基础配置管理
        if UTILS_AVAILABLE:
            super().__init__(config)
            self.performance_monitor = PerformanceMonitor()
            self.exception_handler = UnifiedExceptionHandler("unified_visual_processor")
        else:
            self.config = config

        self.behavior_randomizer = BehaviorRandomizer()

        # Detection mode
        self.detection_mode = getattr(config, 'detection_mode', 'color').lower()

        # Initialize screen capture
        self.screen_capture = mss.mss()

        # Setup capture region
        self._setup_capture_region()

        # Initialize detection systems
        self._initialize_detection_systems()

        # Performance optimization
        self.frame_skip_counter = 0
        self.adaptive_quality = True
        self.last_detection_time = time.time()

        # 使用统一FPS配置，避免硬编码
        target_fps = get_unified_fps_config(config, self.detection_mode)
        self.min_detection_interval = 1.0 / target_fps

        # Initialize FPS calculator with configured target FPS
        self.fps_calculator = FPSCalculator(window_size=30, target_fps=target_fps)
        self._update_target_fps_from_config()

        # FPS display settings
        self.show_fps_in_debug = True
        self.fps_display_position = (10, 30)  # 左上角位置
        self.fps_font = cv2.FONT_HERSHEY_SIMPLEX
        self.fps_font_scale = 0.7
        self.fps_color = (255, 255, 255)  # 白色
        self.fps_thickness = 2



        # Detection history for stability
        self.detection_history = []
        self.max_history_length = getattr(config, 'detection_history_length', 3)
        
        print(f"Unified Visual Processor initialized in {self.detection_mode.upper()} mode")
        # 禁用高级优化器（可能导致性能问题）
        self.use_advanced_optimization = False
        self.advanced_optimizer = None
        print("⚠️ 高级YOLO优化器已禁用，使用标准YOLO检测")


    def _update_target_fps_from_config(self):
        """从配置更新目标FPS，使用统一配置函数避免硬编码"""
        try:
            # 使用统一FPS配置函数
            target_fps = get_unified_fps_config(self.config, self.detection_mode)

            self.fps_calculator.set_target_fps(float(target_fps))
            print(f"✅ 目标FPS已设置为: {target_fps}")

        except Exception as e:
            print(f"❌ 更新目标FPS失败: {e}")
            print(f"   配置对象属性: {[attr for attr in dir(self.config) if not attr.startswith('_')]}")
            # 使用动态默认值而不是硬编码60
            fallback_fps = self.fps_calculator._get_dynamic_default_fps()
            self.fps_calculator.set_target_fps(fallback_fps)

    def _setup_capture_region(self) -> None:
        """Setup the screen capture region."""
        if self.config.auto_detect_resolution:
            self.screen_width = win32api.GetSystemMetrics(0)
            self.screen_height = win32api.GetSystemMetrics(1)
        else:
            self.screen_width = self.config.resolution_x
            self.screen_height = self.config.resolution_y
            
        # Calculate dynamic capture region
        self.capture_region = {
            "top": int((self.screen_height - self.config.capture_region_y) / 2),
            "left": int((self.screen_width - self.config.capture_region_x) / 2),
            "width": self.config.capture_region_x,
            "height": self.config.capture_region_y,
        }
        
        # Add slight randomization to capture region for stealth
        self._randomize_capture_region()
        
    def _randomize_capture_region(self) -> None:
        """Add slight randomization to capture region."""
        if random.random() < 0.1:  # 10% chance to adjust region
            offset_x = random.randint(-5, 5)
            offset_y = random.randint(-5, 5)
            
            self.capture_region["left"] = max(0, self.capture_region["left"] + offset_x)
            self.capture_region["top"] = max(0, self.capture_region["top"] + offset_y)
            
    def _initialize_detection_systems(self):
        """Initialize the appropriate detection systems."""
        # Always initialize color detection as fallback
        self.color_processor = ColorVisualProcessor(self.config)
        
        # Initialize YOLO detection if available and requested
        self.yolo_detector = None
        self.model_manager = None
        self.detection_processor = None
        
        if self.detection_mode in ['yolo', 'hybrid'] and YOLO_AVAILABLE:
            try:
                self.model_manager = ModelManager(self.config)
                self.yolo_detector = YOLODetector(self.config)
                self.detection_processor = DetectionProcessor(self.config)
                
                # Auto-setup model only if no specific model is configured
                model_type = getattr(self.config, 'model_type', '')
                if (hasattr(self.config, 'auto_setup_model') and self.config.auto_setup_model and
                    not model_type):
                    performance_priority = getattr(self.config, 'performance_priority', 'balanced')
                    version_preference = getattr(self.config, 'version_preference', 'stable')
                    success, message = self.model_manager.auto_setup(performance_priority, version_preference)
                    if not success:
                        pass  # Continue with manual model configuration
                        
            except Exception as e:
                print(f"Failed to initialize YOLO detection: {e}")
                self.detection_mode = 'color'  # Fallback to color
                
    def analyze_frame(self, compensation_offset: float = 0) -> Tuple[Optional[Tuple], bool]:
        """Analyze current frame for target identification."""
        start_time = time.time()

        try:
            # 智能帧率控制
            current_time = time.time()
            if current_time - self.last_detection_time < self.min_detection_interval:
                return None, False  # 跳过此帧

            # Capture screen region
            frame_data = self.screen_capture.grab(self.capture_region)

            # Convert to OpenCV format
            frame = cv2.cvtColor(np.array(frame_data), cv2.COLOR_BGRA2BGR)

            # Route to appropriate detection method
            result = None
            if self.detection_mode == 'color':
                result = self._analyze_with_color(frame, compensation_offset)
            elif self.detection_mode == 'yolo':
                result = self._analyze_with_yolo(frame, compensation_offset)
            elif self.detection_mode == 'hybrid':
                result = self._analyze_with_hybrid(frame, compensation_offset)
            else:
                result = self._analyze_with_color(frame, compensation_offset)

            self.last_detection_time = current_time
            return result

        except Exception as e:
            if UTILS_AVAILABLE and hasattr(self, 'exception_handler'):
                self.exception_handler.handle_exception(e, "frame analysis")
            return None, False
        finally:
            # 记录性能数据
            if UTILS_AVAILABLE and hasattr(self, 'performance_monitor'):
                frame_time = time.time() - start_time
                self.performance_monitor.record_frame_time(frame_time)

                # 定期更新内存使用情况
                if random.random() < 0.05:  # 5%的概率
                    self.performance_monitor.update_memory_usage()
            
    def _analyze_with_color(self, frame: np.ndarray, compensation_offset: float) -> Tuple[Optional[Tuple], bool]:
        """Analyze frame using color detection."""
        # Use the existing color processor
        processed_frame = self.color_processor._preprocess_frame(frame)
        target_info, action_trigger = self.color_processor._detect_targets(processed_frame, compensation_offset)
        
        # Update detection history
        self._update_detection_history(target_info)
        
        # Apply stability filtering
        stable_target = self._get_stable_target()
        
        # Handle debug visualization
        if self.config.debug:
            self._show_debug_visualization(frame, processed_frame, stable_target, action_trigger, 'COLOR')
            
        return stable_target, action_trigger
        
    def _analyze_with_yolo(self, frame: np.ndarray, compensation_offset: float) -> Tuple[Optional[Tuple], bool]:
        """Analyze frame using YOLO detection."""
        if not self.yolo_detector or not self.detection_processor:
            return self._analyze_with_color(frame, compensation_offset)
            
        # Get YOLO detections
        detections = self.yolo_detector.detect_targets(frame)

        # Process detections
        frame_center = (self.config.capture_region_x / 2, self.config.capture_region_y / 2)
        target_info = self.detection_processor.process_detections(detections, frame_center)
        
        # Convert YOLO coordinates to relative offset from center
        if target_info:
            x, y, w, h = target_info
            # Convert to relative offset from capture region center
            capture_center_x = self.config.capture_region_x / 2
            capture_center_y = self.config.capture_region_y / 2

            # Apply target_height_ratio adjustment for YOLO mode
            # YOLO returns center coordinates, we need to adjust based on target height ratio
            target_height_ratio = getattr(self.config, 'target_height_ratio', 0.5)

            # Calculate adjusted Y position based on target height ratio
            # 0.0 = top of target, 0.5 = center, 1.0 = bottom of target
            adjusted_y = y + h * (target_height_ratio - 0.5)



            # Calculate offset (negative = left/up, positive = right/down)
            offset_x = x - capture_center_x
            offset_y = adjusted_y - capture_center_y + compensation_offset

            target_info = (offset_x, offset_y, w, h)
        
        # Check action trigger
        action_trigger = self._check_action_trigger_yolo(target_info) if target_info else False
        
        # 减少行为变化以避免左右乱晃
        # 注释掉手部颤抖模拟，减少不必要的抖动
        # if target_info and random.random() < 0.3:  # 30% chance
        #     x, y, w, h = target_info
        #     jitter_x, jitter_y = self.behavior_randomizer.simulate_hand_tremor(0.5)
        #     target_info = (x + jitter_x, y + jitter_y, w, h)
        
        # Update detection history
        self._update_detection_history(target_info)
        
        # Handle debug visualization
        if self.config.debug:
            self._show_debug_visualization_yolo(frame, detections, target_info, action_trigger)
            
        return target_info, action_trigger
        
    def _analyze_with_hybrid(self, frame: np.ndarray, compensation_offset: float) -> Tuple[Optional[Tuple], bool]:
        """Analyze frame using hybrid detection (YOLO + Color)."""
        # Try YOLO first
        yolo_result, yolo_trigger = self._analyze_with_yolo(frame, compensation_offset)
        
        # If YOLO finds something with high confidence, use it
        if yolo_result and hasattr(self, '_last_yolo_confidence'):
            if self._last_yolo_confidence > 0.7:
                return yolo_result, yolo_trigger
        
        # Otherwise, use color detection as backup
        color_result, color_trigger = self._analyze_with_color(frame, compensation_offset)
        
        # Prefer YOLO result if available, otherwise use color
        if yolo_result:
            return yolo_result, yolo_trigger
        else:
            return color_result, color_trigger
            
    def _check_action_trigger_yolo(self, target_info: Optional[Tuple]) -> bool:
        """Check if target is in the action trigger zone for YOLO detection with improved logic."""
        if not target_info:
            return False

        x, y, w, h = target_info
        center_x = self.config.processing_region_x / 2
        center_y = self.config.processing_region_y / 2

        # Use action_threshold from config instead of fixed 30 pixels
        distance_threshold = getattr(self.config, 'action_threshold', 15)
        distance = ((x - center_x)**2 + (y - center_y)**2)**0.5

        # Additional size filter for YOLO targets
        min_target_size = 15
        max_target_size = 120

        size_valid = min_target_size <= max(w, h) <= max_target_size
        distance_valid = distance <= distance_threshold

        return distance_valid and size_valid
        
    def _update_detection_history(self, target_info: Optional[Tuple]) -> None:
        """Update detection history for stability analysis."""
        self.detection_history.append(target_info)
        
        if len(self.detection_history) > self.max_history_length:
            self.detection_history.pop(0)
            
    def _get_stable_target(self) -> Optional[Tuple]:
        """Get stable target based on detection history."""
        if not self.detection_history:
            return None
            
        # Filter out None values
        valid_detections = [d for d in self.detection_history if d is not None]
        
        if len(valid_detections) < 2:
            return self.detection_history[-1]  # Return latest detection
            
        # Calculate average position for stability
        positions = np.array([(d[0], d[1]) for d in valid_detections])
        avg_position = np.mean(positions, axis=0)
        
        # Use dimensions from latest detection
        latest_detection = valid_detections[-1]
        
        return (avg_position[0], avg_position[1], latest_detection[2], latest_detection[3])
        
    def _show_debug_visualization(self, original_frame: np.ndarray, processed_frame: np.ndarray,
                                 target_info: Optional[Tuple], action_trigger: bool, mode: str) -> None:
        """Show debug visualization for color detection."""
        try:
            window_title = f"Visual Processor - {mode}"

            if self.config.display_mode == 'mask':
                display_frame = processed_frame
                if target_info:
                    offset_x, offset_y, w, h = target_info
                    # Convert relative offset back to pixel coordinates for display
                    capture_center_x = self.config.capture_region_x / 2
                    capture_center_y = self.config.capture_region_y / 2
                    pixel_x = offset_x + capture_center_x
                    pixel_y = offset_y + capture_center_y

                    cv2.circle(display_frame, (int(pixel_x), int(pixel_y)), 5, (200, 200, 200), -1)
                    cv2.rectangle(display_frame, (int(pixel_x-w/2), int(pixel_y-h/2)),
                                (int(pixel_x+w/2), int(pixel_y+h/2)), (200, 200, 200), 2)

                # 添加FPS显示
                self._draw_fps_info(display_frame)
                cv2.imshow(window_title, display_frame)
            else:
                display_frame = original_frame.copy()
                if target_info:
                    offset_x, offset_y, w, h = target_info
                    # Convert relative offset back to pixel coordinates for display
                    capture_center_x = self.config.capture_region_x / 2
                    capture_center_y = self.config.capture_region_y / 2
                    pixel_x = offset_x + capture_center_x
                    pixel_y = offset_y + capture_center_y

                    color = (0, 255, 0) if action_trigger else (0, 0, 255)
                    cv2.circle(display_frame, (int(pixel_x), int(pixel_y)), 5, color, -1)
                    cv2.rectangle(display_frame, (int(pixel_x-w/2), int(pixel_y-h/2)),
                                (int(pixel_x+w/2), int(pixel_y+h/2)), color, 2)

                # 添加FPS显示
                self._draw_fps_info(display_frame)
                cv2.imshow(window_title, display_frame)

            cv2.waitKey(1)

        except Exception as e:
            print(f"Error in debug visualization: {e}")
            
    def _show_debug_visualization_yolo(self, frame: np.ndarray, detections: list,
                                      target_info: Optional[Tuple], action_trigger: bool) -> None:
        """Show debug visualization for YOLO detection."""
        try:
            display_frame = frame.copy()

            # Draw all detections
            for detection in detections:
                x1, y1, x2, y2 = detection['bbox']
                confidence = detection['confidence']

                # Draw bounding box
                color = (0, 255, 255)  # Yellow for all detections
                cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 1)

                # Draw confidence
                cv2.putText(display_frame, f"{confidence:.2f}",
                           (int(x1), int(y1-5)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # Draw selected target
            if target_info:
                offset_x, offset_y, w, h = target_info
                # Convert relative offset back to pixel coordinates for display
                capture_center_x = self.config.capture_region_x / 2
                capture_center_y = self.config.capture_region_y / 2
                pixel_x = offset_x + capture_center_x
                pixel_y = offset_y + capture_center_y

                color = (0, 255, 0) if action_trigger else (0, 0, 255)
                cv2.circle(display_frame, (int(pixel_x), int(pixel_y)), 8, color, -1)
                cv2.rectangle(display_frame, (int(pixel_x-w/2), int(pixel_y-h/2)),
                            (int(pixel_x+w/2), int(pixel_y+h/2)), color, 3)

            # 添加FPS显示
            self._draw_fps_info(display_frame)
            cv2.imshow("Visual Processor - YOLO", display_frame)
            cv2.waitKey(1)

        except Exception as e:
            print(f"Error in YOLO debug visualization: {e}")

    def _draw_fps_info(self, frame: np.ndarray) -> None:
        """在调试窗口上绘制FPS信息"""
        if not self.show_fps_in_debug:
            return

        try:
            # 更新FPS计算
            current_fps = self.fps_calculator.update()
            fps_info = self.fps_calculator.get_fps_info()

            # 准备显示文本
            fps_text = f"FPS: {current_fps:.1f}"
            target_text = f"Target: {fps_info['target_fps']:.0f}"
            efficiency_text = f"Efficiency: {fps_info['efficiency']:.1f}%"

            # 绘制半透明背景
            overlay = frame.copy()
            cv2.rectangle(overlay, (5, 5), (250, 100), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

            # 绘制FPS文本
            y_offset = 25
            cv2.putText(frame, fps_text, (10, y_offset), self.fps_font,
                       self.fps_font_scale, self.fps_color, self.fps_thickness)

            y_offset += 25
            cv2.putText(frame, target_text, (10, y_offset), self.fps_font,
                       self.fps_font_scale - 0.1, (200, 200, 200), self.fps_thickness - 1)

            y_offset += 25
            # 效率颜色：绿色(>80%), 黄色(50-80%), 红色(<50%)
            efficiency = fps_info['efficiency']
            if efficiency > 80:
                eff_color = (0, 255, 0)  # 绿色
            elif efficiency > 50:
                eff_color = (0, 255, 255)  # 黄色
            else:
                eff_color = (0, 0, 255)  # 红色

            cv2.putText(frame, efficiency_text, (10, y_offset), self.fps_font,
                       self.fps_font_scale - 0.1, eff_color, self.fps_thickness - 1)

        except Exception as e:
            print(f"⚠️ FPS显示错误: {e}")

    def switch_detection_mode(self, new_mode: str) -> bool:
        """Switch detection mode at runtime."""
        new_mode = new_mode.lower()
        
        if new_mode not in ['color', 'yolo', 'hybrid']:
            print(f"Invalid detection mode: {new_mode}")
            return False
            
        if new_mode in ['yolo', 'hybrid'] and not YOLO_AVAILABLE:
            print("YOLO detection not available. Staying in color mode.")
            return False
            
        self.detection_mode = new_mode
        print(f"Switched to {new_mode.upper()} detection mode")
        return True
        
    def get_detection_info(self) -> dict:
        """Get information about current detection setup."""
        info = {
            'detection_mode': self.detection_mode,
            'yolo_available': YOLO_AVAILABLE,
            'color_processor_active': self.color_processor is not None,
            'yolo_detector_active': self.yolo_detector is not None
        }

        if self.yolo_detector:
            info['yolo_model_info'] = self.yolo_detector.get_model_info()

        if self.detection_processor:
            info['tracking_stats'] = self.detection_processor.get_tracking_stats()

        return info

    def update_config(self, new_config) -> None:
        """Update visual processor configuration without recreating the instance."""
        # 使用基础配置管理
        if UTILS_AVAILABLE and hasattr(self, 'update_config'):
            super().update_config(new_config)
        else:
            self.config = new_config

        # 更新性能参数
        self.min_detection_interval = 1.0 / getattr(new_config, 'max_fps', 60)
        self.max_history_length = getattr(new_config, 'detection_history_length', 3)

        # 更新FPS设置
        self._update_target_fps_from_config()

        # Update YOLO detector configuration if available
        if self.yolo_detector:
            self.yolo_detector.update_config(new_config)

        # Update color processor configuration if available
        if self.color_processor:
            self.color_processor.config = new_config

        # Update detection processor configuration if available
        if self.detection_processor:
            self.detection_processor.config = new_config

        print(f"✅ 视觉处理器配置已更新")

    def get_fps_info(self) -> Dict[str, float]:
        """获取FPS信息"""
        return self.fps_calculator.get_fps_info()

    def set_fps_display(self, show: bool) -> None:
        """设置是否显示FPS信息"""
        self.show_fps_in_debug = show
        print(f"🖼️ FPS显示已{'启用' if show else '禁用'}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {}

        # 基础统计
        if UTILS_AVAILABLE and hasattr(self, 'performance_monitor'):
            stats['unified_processor'] = self.performance_monitor.get_stats()

        # FPS统计
        stats['fps_info'] = self.get_fps_info()
        stats['detection_mode'] = self.detection_mode

        # YOLO检测器统计
        if hasattr(self, 'yolo_detector') and self.yolo_detector:
            if hasattr(self.yolo_detector, 'get_performance_stats'):
                stats['yolo_detector'] = self.yolo_detector.get_performance_stats()

        # 检测历史统计
        stats['detection_history'] = {
            'length': len(self.detection_history),
            'max_length': self.max_history_length,
            'detection_mode': self.detection_mode
        }

        # 错误统计
        if UTILS_AVAILABLE and hasattr(self, 'exception_handler'):
            stats['errors'] = self.exception_handler.get_error_stats()

        return stats
        
    # 移除运行时置信度更新方法，使用传统配置模式

    def __del__(self):
        """Cleanup resources."""
        try:
            cv2.destroyAllWindows()
        except:
            pass
