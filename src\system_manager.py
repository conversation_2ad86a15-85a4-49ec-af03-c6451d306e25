"""
    System Manager - Configuration and input state management
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import win32api
import time
import sys
from typing import Optional
from settings_manager import SettingsManager


class SystemManager:
    """Manages system configuration and input state monitoring."""

    def __init__(self):
        """Initialize the system manager."""
        self.settings_manager = SettingsManager()
        self.config = self.settings_manager

        # System state variables
        self.enhancement_active = False
        self.compensation_active = False
        self.auto_action_active = False
        self.rapid_input_active = False

        # Key bindings from configuration
        self._load_key_bindings()

        # Timing control - 优化按键检测灵敏度
        self.key_check_delay = 0.05  # 50ms delay between key checks (提高灵敏度)
        self.last_key_check = {}

        # 不同按键使用不同的检测延迟
        self.key_delays = {
            'system_keys': 0.1,      # 系统键(F1-F4)使用较长延迟防止误触
            'activation_keys': 0.01,  # 激活键使用最短延迟提高响应
            'action_keys': 0.02      # 动作键使用短延迟
        }

        # Configuration snapshot for change detection
        self.config_snapshot = self._create_config_snapshot()
        
    def _load_key_bindings(self) -> None:
        """Load key bindings from configuration."""
        try:
            self.key_reload_settings = self.config.key_reload_settings
            self.key_toggle_enhancement = self.config.key_toggle_enhancement
            self.key_toggle_compensation = self.config.key_toggle_compensation
            self.key_system_exit = self.config.key_system_exit
            self.key_auto_action = self.config.key_auto_action
            self.key_rapid_input = self.config.key_rapid_input
            self.enhancement_activation_keys = self.config.enhancement_activation_keys
        except AttributeError as e:
            self._set_default_key_bindings()
            
    def _set_default_key_bindings(self) -> None:
        """Set default key bindings if configuration is missing."""
        self.key_reload_settings = 0x70  # F1
        self.key_toggle_enhancement = 0x71  # F2
        self.key_toggle_compensation = 0x72  # F3
        self.key_system_exit = 0x73  # F4
        self.key_auto_action = 0x06  # Mouse 4
        self.key_rapid_input = 0x05  # Mouse 5
        self.enhancement_activation_keys = [0x01, 0x02]  # Mouse 1 & 2
        
    def check_system_controls(self) -> bool:
        """Check system control keys and handle state changes."""
        current_time = time.time()
        
        # Check reload configuration key - 使用系统键延迟
        if self._is_key_pressed_with_delay(self.key_reload_settings, current_time, 'system_keys'):
            return True  # Signal configuration reload needed

        # Check enhancement toggle - 使用系统键延迟
        if self._is_key_pressed_with_delay(self.key_toggle_enhancement, current_time, 'system_keys'):
            self.enhancement_active = not self.enhancement_active
            print(f"Enhancement: {self.enhancement_active}")

        # Check compensation toggle - 使用系统键延迟
        if self._is_key_pressed_with_delay(self.key_toggle_compensation, current_time, 'system_keys'):
            self.compensation_active = not self.compensation_active
            print(f"Compensation: {self.compensation_active}")

        # Check system exit - 使用系统键延迟
        if self._is_key_pressed_with_delay(self.key_system_exit, current_time, 'system_keys'):
            print("System shutdown initiated")
            sys.exit(0)
            
        return False
        
    def _is_key_pressed_with_delay(self, key_code: int, current_time: float, key_type: str = 'system_keys') -> bool:
        """Check if key is pressed with delay to prevent rapid triggering."""
        if win32api.GetAsyncKeyState(key_code) < 0:
            last_press_time = self.last_key_check.get(key_code, 0)
            delay = self.key_delays.get(key_type, self.key_check_delay)
            if current_time - last_press_time > delay:
                self.last_key_check[key_code] = current_time
                return True
        return False
        
    def reload_configuration(self) -> None:
        """Reload system configuration."""
        try:
            self.settings_manager.reload_settings()
            self._load_key_bindings()
        except Exception as e:
            pass  # Configuration reload error ignored

    def _create_config_snapshot(self) -> dict:
        """Create a snapshot of current configuration for change detection."""
        return {
            # Hot-reloadable configurations
            'movement_speed': getattr(self.config, 'movement_speed', 1.0),
            'vertical_speed_multiplier': getattr(self.config, 'vertical_speed_multiplier', 1.0),
            'movement_smoothing_factor': getattr(self.config, 'movement_smoothing_factor', 0.8),
            'confidence_threshold': getattr(self.config, 'confidence_threshold', 0.5),
            'nms_threshold': getattr(self.config, 'nms_threshold', 0.4),
            'compensation_x': getattr(self.config, 'compensation_x', 0.0),
            'compensation_y': getattr(self.config, 'compensation_y', 0.0),
            'recovery_rate': getattr(self.config, 'recovery_rate', 0.0),
            'action_delay': getattr(self.config, 'action_delay', 0),
            'action_randomization': getattr(self.config, 'action_randomization', 30),
            'action_threshold': getattr(self.config, 'action_threshold', 8),
            'target_input_rate': getattr(self.config, 'target_input_rate', 10),
            'debug': getattr(self.config, 'debug', False),
            'debug_always_on': getattr(self.config, 'debug_always_on', False),
            'target_height_ratio': getattr(self.config, 'target_height_ratio', 0.5),

            # PID控制参数 - 支持热重载
            'control_method': getattr(self.config, 'control_method', 'adaptive'),
            'pid_kp': getattr(self.config, 'pid_kp', 0.6),
            'pid_ki': getattr(self.config, 'pid_ki', 0.05),
            'pid_kd': getattr(self.config, 'pid_kd', 0.15),
            'pid_integral_limit': getattr(self.config, 'pid_integral_limit', 50.0),
            'pid_max_output': getattr(self.config, 'pid_max_output', 30.0),
            'debug_pid': getattr(self.config, 'debug_pid', False),
            'debug_movement': getattr(self.config, 'debug_movement', False),

            # 自主控制参数 - 支持热重载
            'autonomous_control_enabled': getattr(self.config, 'autonomous_control_enabled', True),
            'approach_distance_threshold': getattr(self.config, 'approach_distance_threshold', 35.0),
            'decelerate_distance_threshold': getattr(self.config, 'decelerate_distance_threshold', 12.0),
            'precision_distance_threshold': getattr(self.config, 'precision_distance_threshold', 3.0),
            'approach_speed_multiplier': getattr(self.config, 'approach_speed_multiplier', 0.8),
            'decelerate_speed_multiplier': getattr(self.config, 'decelerate_speed_multiplier', 0.3),
            'precision_speed_multiplier': getattr(self.config, 'precision_speed_multiplier', 0.08),

            # Restart-required configurations
            'detection_mode': getattr(self.config, 'detection_mode', 'color'),
            'model_type': getattr(self.config, 'model_type', 'yolov8n'),
            'model_format': getattr(self.config, 'model_format', 'auto'),
            'input_method': getattr(self.config, 'input_method', 'winapi'),
            'capture_region_x': getattr(self.config, 'capture_region_x', 256),
            'capture_region_y': getattr(self.config, 'capture_region_y', 256),
        }

    def detect_config_changes(self) -> tuple[bool, bool]:
        """Detect configuration changes and return (has_hot_reload_changes, has_restart_changes)."""
        new_snapshot = self._create_config_snapshot()
        old_snapshot = self.config_snapshot

        # Define which configs require restart
        restart_configs = {
            'detection_mode', 'model_type', 'model_format',
            'input_method', 'capture_region_x', 'capture_region_y'
        }

        has_hot_reload_changes = False
        has_restart_changes = False

        for key, new_value in new_snapshot.items():
            old_value = old_snapshot.get(key)
            if old_value != new_value:
                if key in restart_configs:
                    has_restart_changes = True
                else:
                    has_hot_reload_changes = True

        # Update snapshot
        self.config_snapshot = new_snapshot

        return has_hot_reload_changes, has_restart_changes
            
    def get_configuration(self):
        """Get current system configuration."""
        return self.config
        
    def get_enhancement_state(self) -> bool:
        """Get current enhancement state with activation key check."""
        if not self.enhancement_active:
            return False

        # Check if enhancement activation keys are pressed
        if self.enhancement_activation_keys[0] == 'off':
            return True
        else:
            for key in self.enhancement_activation_keys:
                if win32api.GetAsyncKeyState(key) < 0:
                    return True
        return False

    def get_continuous_enhancement_state(self) -> tuple[bool, float]:
        """
        获取持续自瞄状态和持续时间

        Returns:
            (is_active, duration): 是否激活和持续时间
        """
        if not hasattr(self, '_enhancement_start_time'):
            self._enhancement_start_time = 0.0
            self._last_enhancement_state = False

        current_state = self.get_enhancement_state()
        current_time = time.time()

        if current_state and not self._last_enhancement_state:
            # 刚开始按下
            self._enhancement_start_time = current_time
            duration = 0.0
        elif current_state and self._last_enhancement_state:
            # 持续按下
            duration = current_time - self._enhancement_start_time
        else:
            # 没有按下或刚松开
            duration = 0.0
            self._enhancement_start_time = 0.0

        self._last_enhancement_state = current_state
        return current_state, duration
        
    def get_auto_action_state(self) -> bool:
        """Get current auto action state."""
        return win32api.GetAsyncKeyState(self.key_auto_action) < 0
        
    def get_rapid_input_state(self) -> bool:
        """Get current rapid input state."""
        return win32api.GetAsyncKeyState(self.key_rapid_input) < 0
        
    def get_compensation_state(self) -> bool:
        """Get current compensation state."""
        return self.compensation_active
        
    def set_enhancement_state(self, state: bool) -> None:
        """Set enhancement state."""
        self.enhancement_active = state
        
    def set_compensation_state(self, state: bool) -> None:
        """Set compensation state."""
        self.compensation_active = state
        
    def get_system_status(self) -> dict:
        """Get comprehensive system status."""
        return {
            'enhancement_active': self.enhancement_active,
            'compensation_active': self.compensation_active,
            'auto_action_available': self.get_auto_action_state(),
            'rapid_input_available': self.get_rapid_input_state(),
            'configuration_loaded': self.config is not None
        }
