"""
    Settings Manager - Configuration file management
    Copyright (C) 2025 Development Team

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERC<PERSON><PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
"""
import os
from configparser import ConfigParser
from typing import List, Optional


class SettingsManager:
    """Manages application settings and configuration."""
    
    def __init__(self):
        """Initialize the settings manager."""
        self.parser = ConfigParser()
        
        # Input/Output settings
        self.input_method = None
        self.device_ip = None
        self.device_port = None
        self.serial_port = None

        # Enhanced ARD settings
        self.hardware_type = None
        self.ard_serial_port = None
        self.ard_baud_rate = None
        self.stealth_profile = None
        self.behavior_pattern = None
        self.stealth_level = None
        self.silent_mode = None
        
        # Detection mode settings
        self.detection_mode = None
        self.performance_priority = None
        self.auto_setup_model = None

        # Color detection settings
        self.target_grouping_threshold = None
        self.color_range_upper = None
        self.color_range_lower = None

        # YOLO detection settings
        self.model_type = None
        self.model_format = None
        self.model_path = None  # Optional custom model path
        self.confidence_threshold = None
        self.nms_threshold = None
        self.target_classes = None
        self.use_gpu = None
        self.batch_size = None

        # Enhanced model selection settings
        self.model_filename = None
        self.model_selection_strategy = None
        self.version_preference = None
        self.performance_priority = None
        self.fallback_behavior = None
        self.scan_subdirectories = None
        self.validate_models = None
        self.cache_model_info = None

        # Extended YOLO settings
        self.enable_anti_detection = None
        self.randomize_timing = None
        self.cache_randomization = None
        self.target_priority = None
        self.max_target_distance = None
        self.position_threshold = None
        self.smoothing_enabled = None
        self.smoothing_factor = None

        # Common visual processing settings
        self.capture_region_x = None
        self.capture_region_y = None
        self.processing_region_x = None
        self.processing_region_y = None
        self.min_frame_time = None
        self.auto_detect_resolution = None
        self.resolution_x = None
        self.resolution_y = None

        # Performance settings
        self.max_fps = None
        self.cache_size = None
        self.cache_ttl = None
        self.detection_history_length = None
        self.movement_history_length = None
        
        # Movement settings
        self.display_center_offset = None
        self.movement_smoothing_factor = None
        self.movement_speed = None
        self.vertical_speed_multiplier = None
        self.target_height_ratio = None
        
        # Compensation settings
        self.compensation_mode = None
        self.compensation_x = None
        self.compensation_y = None
        self.max_compensation_offset = None
        self.compensation_recovery = None
        # 延时压枪参数
        self.delay_before_compensation = None
        self.enhanced_compensation_multiplier = None
        self.light_compensation_factor = None
        
        # Action settings
        self.action_delay = None
        self.action_randomization = None
        self.action_threshold = None
        
        # Input rate settings
        self.target_input_rate = None
        
        # Key bindings
        self.key_reload_settings = None
        self.key_toggle_enhancement = None
        self.key_toggle_compensation = None
        self.key_system_exit = None
        self.key_auto_action = None
        self.key_rapid_input = None
        self.enhancement_activation_keys = []
        
        # Debug settings
        self.debug = None
        self.debug_always_on = None
        self.display_mode = None
        
        # Load configuration
        self.settings_path = self._get_resource_path('settings.ini')
        self._load_settings()

    def _get_resource_path(self, relative_path):
        """获取资源文件路径（支持打包后的exe）"""
        import sys
        import os

        # 检查环境变量
        if relative_path == 'settings.ini':
            env_path = os.environ.get('UNIBOT_CONFIG_PATH')
            if env_path and os.path.exists(env_path):
                return env_path

        try:
            # PyInstaller打包后的路径
            base_path = sys._MEIPASS
        except AttributeError:
            # 开发环境路径
            base_path = os.path.dirname(__file__)
            if relative_path == 'settings.ini':
                base_path = os.path.dirname(base_path)  # 回到项目根目录

        return os.path.join(base_path, relative_path)

    def _load_settings(self) -> None:
        """Load settings from configuration file."""
        try:
            self.parser.read(self.settings_path, encoding='utf-8')
            self._parse_all_sections()
        except Exception as e:
            print(f"Error loading settings: {e}")
            self._create_default_settings()
            
    def _parse_all_sections(self) -> None:
        """Parse all configuration sections."""
        self._parse_input_settings()  # 新的输入配置解析
        self._parse_movement_settings()  # 新的移动配置解析
        self._parse_enhancement_settings()  # 保留兼容性
        self._parse_device_settings()
        self._parse_kmboxnet_settings()  # KmBoxNet配置解析
        self._parse_detection_settings()
        self._parse_visual_color_settings()
        self._parse_visual_yolo_settings()
        self._parse_visual_common_settings()
        self._parse_performance_settings()
        self._parse_compensation_settings()
        self._parse_action_settings()
        self._parse_input_rate_settings()
        self._parse_key_bindings()
        self._parse_debug_settings()
        self._parse_tyolo_optimization_settings()

    def _parse_input_settings(self) -> None:
        """Parse input method settings from [input] section with fallback to [enhancement]."""
        try:
            # 首先尝试从新的[input]节读取
            if self.parser.has_section('input') and self.parser.has_option('input', 'input_method'):
                value = self.parser.get('input', 'input_method').lower()
            # 向后兼容：从[enhancement]节读取
            elif self.parser.has_section('enhancement') and self.parser.has_option('enhancement', 'input_method'):
                value = self.parser.get('enhancement', 'input_method').lower()
                print("⚠️ 使用旧配置格式 [enhancement].input_method，建议更新到 [input].input_method")
            else:
                value = 'win32'  # 默认值
                print("⚠️ 未找到input_method配置，使用默认值: win32")

            # 映射配置值到内部值
            value_mapping = {
                'winapi': 'winapi',
                'win32': 'winapi',  # 新的配置值映射
                'sendinput': 'sendinput',  # SendInput API方法
                'thirdparty': 'thirdparty',  # 第三方库方法
                'kmboxnet': 'kmboxnet',  # KmBoxNet网络硬件方法
                'interception': 'interception_driver',  # 新的配置值映射
                'interception_driver': 'interception_driver',
                'microcontroller_serial': 'microcontroller_serial',
                'microcontroller_socket': 'microcontroller_socket',
                'enhanced_ard': 'enhanced_ard',  # 增强ARD系统
                'hardware': 'microcontroller_serial'  # 新的配置值映射
            }

            if value in value_mapping:
                self.input_method = value_mapping[value]
                print(f"✅ 输入方法设置为: {self.input_method}")
            else:
                print(f'⚠️ 无效的input_method值: {value}，使用默认值: winapi')
                self.input_method = 'winapi'

        except Exception as e:
            print(f"❌ 解析输入设置失败: {e}")
            self.input_method = 'winapi'

    def _parse_movement_settings(self) -> None:
        """Parse movement settings from [movement] section with fallback to [enhancement]."""
        try:
            # 移动速度 - 优先读取[enhancement]节（GUI设置的位置）
            if self.parser.has_section('enhancement') and self.parser.has_option('enhancement', 'movement_speed'):
                self.movement_speed = float(self.parser.get('enhancement', 'movement_speed'))
            elif self.parser.has_section('movement') and self.parser.has_option('movement', 'movement_speed'):
                self.movement_speed = float(self.parser.get('movement', 'movement_speed'))
            else:
                self.movement_speed = 1.0

            # 移动平滑因子
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'movement_smoothing_factor'):
                value = float(self.parser.get('movement', 'movement_smoothing_factor'))
            elif self.parser.has_section('enhancement') and self.parser.has_option('enhancement', 'movement_smoothing_factor'):
                value = float(self.parser.get('enhancement', 'movement_smoothing_factor'))
            else:
                value = 0.3

            # 修复：移除特殊转换公式，直接使用配置值
            if 0 <= value <= 1:
                self.movement_smoothing_factor = value  # 直接使用原始值
            else:
                print('⚠️ 无效的movement_smoothing_factor值')
                self.movement_smoothing_factor = 0.3  # 使用合理的默认值

            # 垂直速度倍数
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'vertical_speed_multiplier'):
                self.vertical_speed_multiplier = float(self.parser.get('movement', 'vertical_speed_multiplier'))
            elif self.parser.has_section('enhancement') and self.parser.has_option('enhancement', 'vertical_speed_multiplier'):
                self.vertical_speed_multiplier = float(self.parser.get('enhancement', 'vertical_speed_multiplier'))
            else:
                self.vertical_speed_multiplier = 0.8

            # 控制方法
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'control_method'):
                self.control_method = self.parser.get('movement', 'control_method')
            else:
                self.control_method = 'adaptive'

            # 自主控制设置
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'autonomous_control_enabled'):
                self.autonomous_control_enabled = self.parser.getboolean('movement', 'autonomous_control_enabled')
            else:
                self.autonomous_control_enabled = True

            # 距离阈值设置
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'approach_distance_threshold'):
                self.approach_distance_threshold = float(self.parser.get('movement', 'approach_distance_threshold'))
            else:
                self.approach_distance_threshold = 35.0

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'decelerate_distance_threshold'):
                self.decelerate_distance_threshold = float(self.parser.get('movement', 'decelerate_distance_threshold'))
            else:
                self.decelerate_distance_threshold = 12.0

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'precision_distance_threshold'):
                self.precision_distance_threshold = float(self.parser.get('movement', 'precision_distance_threshold'))
            else:
                self.precision_distance_threshold = 3.0

            # 速度倍率设置
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'approach_speed_multiplier'):
                self.approach_speed_multiplier = float(self.parser.get('movement', 'approach_speed_multiplier'))
            else:
                self.approach_speed_multiplier = 0.8

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'decelerate_speed_multiplier'):
                self.decelerate_speed_multiplier = float(self.parser.get('movement', 'decelerate_speed_multiplier'))
            else:
                self.decelerate_speed_multiplier = 0.3

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'precision_speed_multiplier'):
                self.precision_speed_multiplier = float(self.parser.get('movement', 'precision_speed_multiplier'))
            else:
                self.precision_speed_multiplier = 0.08

            # PID控制参数
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'pid_kp'):
                self.pid_kp = float(self.parser.get('movement', 'pid_kp'))
            else:
                self.pid_kp = 0.6

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'pid_ki'):
                self.pid_ki = float(self.parser.get('movement', 'pid_ki'))
            else:
                self.pid_ki = 0.05

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'pid_kd'):
                self.pid_kd = float(self.parser.get('movement', 'pid_kd'))
            else:
                self.pid_kd = 0.15

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'pid_integral_limit'):
                self.pid_integral_limit = float(self.parser.get('movement', 'pid_integral_limit'))
            else:
                self.pid_integral_limit = 50.0

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'pid_max_output'):
                self.pid_max_output = float(self.parser.get('movement', 'pid_max_output'))
            else:
                self.pid_max_output = 30.0

            # 调试设置
            if self.parser.has_section('movement') and self.parser.has_option('movement', 'debug_movement'):
                self.debug_movement = self.parser.getboolean('movement', 'debug_movement')
            else:
                self.debug_movement = False

            if self.parser.has_section('movement') and self.parser.has_option('movement', 'debug_pid'):
                self.debug_pid = self.parser.getboolean('movement', 'debug_pid')
            else:
                self.debug_pid = False

        except Exception as e:
            print(f"❌ 解析移动设置失败: {e}")
            # 设置默认值
            self.movement_speed = 1.0
            self.movement_smoothing_factor = 0.76
            self.vertical_speed_multiplier = 0.8

    def _parse_enhancement_settings(self) -> None:
        """Parse legacy enhancement-related settings (for backward compatibility)."""
        try:
            # 显示中心偏移（保留在enhancement节）
            if self.parser.has_option('enhancement', 'display_center_offset'):
                self.display_center_offset = int(self.parser.get('enhancement', 'display_center_offset'))
            else:
                self.display_center_offset = 0

            # 目标高度比例（保留在enhancement节）
            if self.parser.has_option('enhancement', 'target_height_ratio'):
                value = float(self.parser.get('enhancement', 'target_height_ratio'))
                if 0 <= value <= 1:
                    self.target_height_ratio = value
                else:
                    print('⚠️ 无效的target_height_ratio值')
                    self.target_height_ratio = 0.5
            else:
                self.target_height_ratio = 0.5

        except Exception as e:
            print(f"❌ 解析enhancement设置失败: {e}")
            # 设置默认值
            self.display_center_offset = 0
            self.target_height_ratio = 0.5
            
    def _parse_device_settings(self) -> None:
        """Parse device communication settings."""
        try:
            if self.input_method == 'microcontroller_socket':
                self.device_ip = self.parser.get('device', 'device_ip')
                self.device_port = int(self.parser.get('device', 'device_port'))
            elif self.input_method == 'microcontroller_serial':
                self.serial_port = self.parser.get('device', 'serial_port')
            elif self.input_method == 'enhanced_ard':
                self._parse_enhanced_ard_settings()
        except Exception as e:
            print(f"Error parsing device settings: {e}")

    def _parse_enhanced_ard_settings(self) -> None:
        """Parse enhanced ARD settings from [enhanced_ard] section."""
        try:
            # 硬件类型
            self.hardware_type = self.parser.get('enhanced_ard', 'hardware_type', fallback='stealth_ard')

            # 串口配置
            self.ard_serial_port = self.parser.get('enhanced_ard', 'ard_serial_port', fallback='COM11')
            self.ard_baud_rate = int(self.parser.get('enhanced_ard', 'ard_baud_rate', fallback='115200'))

            # 隐蔽模式配置
            self.stealth_profile = self.parser.get('enhanced_ard', 'stealth_profile', fallback='logitech_g502')
            self.behavior_pattern = self.parser.get('enhanced_ard', 'behavior_pattern', fallback='human_like')
            self.stealth_level = self.parser.get('enhanced_ard', 'stealth_level', fallback='high')
            self.silent_mode = self.parser.get('enhanced_ard', 'silent_mode', fallback='true').lower() == 'true'

            print(f"✅ 增强ARD配置已加载: {self.hardware_type} @ {self.ard_serial_port}")
            print(f"   伪装设备: {self.stealth_profile}, 隐蔽等级: {self.stealth_level}")

        except Exception as e:
            print(f"❌ 解析增强ARD设置失败: {e}")
            # 设置默认值
            self.hardware_type = 'stealth_ard'
            self.ard_serial_port = 'COM11'
            self.ard_baud_rate = 115200
            self.stealth_profile = 'logitech_g502'
            self.behavior_pattern = 'human_like'
            self.stealth_level = 'high'
            self.silent_mode = True
            
    def _parse_detection_settings(self) -> None:
        """Parse detection mode settings."""
        try:
            self.detection_mode = self.parser.get('detection', 'detection_mode').lower()
            self.performance_priority = self.parser.get('detection', 'performance_priority').lower()
            self.auto_setup_model = self.parser.get('detection', 'auto_setup_model').lower() == 'true'
        except Exception as e:
            print(f"Error parsing detection settings: {e}")
            # Set defaults
            self.detection_mode = 'color'
            self.performance_priority = 'balanced'
            self.auto_setup_model = True

    def _parse_visual_color_settings(self) -> None:
        """Parse color-based visual processing settings."""
        try:
            # Parse grouping threshold
            threshold_str = self.parser.get('visual_color', 'target_grouping_threshold')
            self.target_grouping_threshold = [int(x.strip()) for x in threshold_str.split(',')]

            # Parse color ranges
            upper_str = self.parser.get('visual_color', 'color_range_upper')
            self.color_range_upper = tuple(int(x.strip()) for x in upper_str.split(','))

            lower_str = self.parser.get('visual_color', 'color_range_lower')
            self.color_range_lower = tuple(int(x.strip()) for x in lower_str.split(','))

        except Exception as e:
            print(f"Error parsing color visual settings: {e}")

    def _parse_visual_yolo_settings(self) -> None:
        """Parse YOLO-based visual processing settings with extended support."""
        try:
            # Core model settings
            self.model_type = self.parser.get('visual_yolo', 'model_type')

            # Model format preference (new setting)
            self.model_format = self.parser.get('visual_yolo', 'model_format', fallback='auto')

            # Enhanced model selection options

            # Method 1: Exact model filename (highest priority)
            if self.parser.has_option('visual_yolo', 'model_filename'):
                model_filename_value = self.parser.get('visual_yolo', 'model_filename')
                if model_filename_value and not model_filename_value.strip().startswith('#'):
                    self.model_filename = model_filename_value.strip()
                else:
                    self.model_filename = None
            else:
                self.model_filename = None

            # Method 2: Custom model path (second priority)
            if self.parser.has_option('visual_yolo', 'model_path'):
                model_path_value = self.parser.get('visual_yolo', 'model_path')
                # Only set if not commented out and not empty
                if model_path_value and not model_path_value.strip().startswith('#'):
                    self.model_path = model_path_value
                else:
                    self.model_path = None
            else:
                self.model_path = None

            # Method 3-5: Selection strategies
            self.model_selection_strategy = self.parser.get('visual_yolo', 'model_selection_strategy', fallback='prefer_onnx')
            self.version_preference = self.parser.get('visual_yolo', 'version_preference', fallback='stable')
            self.performance_priority = self.parser.get('visual_yolo', 'performance_priority', fallback='balanced')
            self.fallback_behavior = self.parser.get('visual_yolo', 'fallback_behavior', fallback='flexible')

            # Model discovery settings
            self.scan_subdirectories = self.parser.get('visual_yolo', 'scan_subdirectories', fallback='false').lower() == 'true'
            self.validate_models = self.parser.get('visual_yolo', 'validate_models', fallback='true').lower() == 'true'
            self.cache_model_info = self.parser.get('visual_yolo', 'cache_model_info', fallback='true').lower() == 'true'

            # Detection thresholds
            self.confidence_threshold = float(self.parser.get('visual_yolo', 'confidence_threshold'))
            self.nms_threshold = float(self.parser.get('visual_yolo', 'nms_threshold'))

            # Parse target classes
            classes_str = self.parser.get('visual_yolo', 'target_classes')
            # Remove brackets and parse as list
            classes_str = classes_str.strip('[]')
            self.target_classes = [int(x.strip()) for x in classes_str.split(',')]

            # Performance settings
            self.use_gpu = self.parser.get('visual_yolo', 'use_gpu').lower() == 'true'
            self.batch_size = int(self.parser.get('visual_yolo', 'batch_size'))

            # Target selection settings
            self.target_priority = self.parser.get('visual_yolo', 'target_priority')
            self.max_target_distance = float(self.parser.get('visual_yolo', 'max_target_distance'))
            self.position_threshold = float(self.parser.get('visual_yolo', 'position_threshold'))
            self.smoothing_enabled = self.parser.get('visual_yolo', 'smoothing_enabled').lower() == 'true'
            self.smoothing_factor = float(self.parser.get('visual_yolo', 'smoothing_factor'))

            # Anti-detection settings (new)
            self.enable_anti_detection = self.parser.get('visual_yolo', 'enable_anti_detection', fallback='true').lower() == 'true'
            self.randomize_timing = self.parser.get('visual_yolo', 'randomize_timing', fallback='true').lower() == 'true'
            self.cache_randomization = self.parser.get('visual_yolo', 'cache_randomization', fallback='true').lower() == 'true'

        except Exception as e:
            print(f"Error parsing YOLO visual settings: {e}")

    def _parse_visual_common_settings(self) -> None:
        """Parse common visual processing settings."""
        try:
            # Parse region settings - 优先从[capture]节读取，回退到[visual_common]节
            if self.parser.has_section('capture'):
                self.capture_region_x = int(self.parser.get('capture', 'capture_region_x', fallback='640'))
                self.capture_region_y = int(self.parser.get('capture', 'capture_region_y', fallback='640'))
                print(f"✅ 从[capture]节读取捕获区域: {self.capture_region_x}x{self.capture_region_y}")
            elif self.parser.has_section('visual_common'):
                self.capture_region_x = int(self.parser.get('visual_common', 'capture_region_x', fallback='640'))
                self.capture_region_y = int(self.parser.get('visual_common', 'capture_region_y', fallback='640'))
                print(f"✅ 从[visual_common]节读取捕获区域: {self.capture_region_x}x{self.capture_region_y}")
            else:
                self.capture_region_x = 640
                self.capture_region_y = 640
                print(f"⚠️ 使用默认捕获区域: {self.capture_region_x}x{self.capture_region_y}")

            # Parse processing region settings
            if self.parser.has_section('visual_common'):
                self.processing_region_x = int(self.parser.get('visual_common', 'processing_region_x', fallback=str(self.capture_region_x)))
                self.processing_region_y = int(self.parser.get('visual_common', 'processing_region_y', fallback=str(self.capture_region_y)))
            else:
                self.processing_region_x = self.capture_region_x
                self.processing_region_y = self.capture_region_y

            # Parse timing settings
            if self.parser.has_section('visual_common'):
                max_fps = int(self.parser.get('visual_common', 'max_frames_per_sec', fallback=str(self._get_dynamic_default_fps())))
                self.min_frame_time = 1000 / max_fps if max_fps > 0 else (1000 / self._get_dynamic_default_fps())

                # Parse resolution settings
                self.auto_detect_resolution = self.parser.get('visual_common', 'auto_detect_resolution', fallback='false').lower() == 'true'
                self.resolution_x = int(self.parser.get('visual_common', 'resolution_x', fallback='1920'))
                self.resolution_y = int(self.parser.get('visual_common', 'resolution_y', fallback='1080'))
            else:
                # 使用动态默认值
                default_fps = self._get_dynamic_default_fps()
                self.min_frame_time = 1000 / default_fps
                self.auto_detect_resolution = False
                self.resolution_x = 1920
                self.resolution_y = 1080
                print(f"⚠️ [visual_common]节不存在，使用动态默认FPS: {default_fps}")

        except Exception as e:
            print(f"❌ 解析visual_common设置失败: {e}")
            # 设置默认值
            if not hasattr(self, 'capture_region_x'):
                self.capture_region_x = 640
                self.capture_region_y = 640
            if not hasattr(self, 'processing_region_x'):
                self.processing_region_x = self.capture_region_x
                self.processing_region_y = self.capture_region_y
            self.min_frame_time = 16.67
            self.auto_detect_resolution = False
            self.resolution_x = 1920
            self.resolution_y = 1080

    def _get_dynamic_default_fps(self) -> int:
        """根据系统性能动态设置默认FPS"""
        try:
            import psutil
            cpu_count = psutil.cpu_count()
            if cpu_count >= 8:
                return 120  # 高性能系统
            elif cpu_count >= 4:
                return 90   # 中等性能系统
            else:
                return 60   # 低性能系统
        except ImportError:
            return 60  # psutil不可用时的回退

    def _parse_performance_settings(self) -> None:
        """Parse performance settings from [performance] section."""
        try:
            if self.parser.has_section('performance'):
                # 解析FPS设置，使用动态默认值
                default_fps = self._get_dynamic_default_fps()
                self.max_fps = int(self.parser.get('performance', 'max_fps', fallback=str(default_fps)))
                print(f"✅ 从[performance]节读取max_fps: {self.max_fps}")

                # 解析缓存设置
                self.cache_size = int(self.parser.get('performance', 'cache_size', fallback='50'))
                self.cache_ttl = float(self.parser.get('performance', 'cache_ttl', fallback='0.1'))

                # 解析历史长度设置
                self.detection_history_length = int(self.parser.get('performance', 'detection_history_length', fallback='3'))
                self.movement_history_length = int(self.parser.get('performance', 'movement_history_length', fallback='5'))

                print(f"✅ 性能设置已加载: FPS={self.max_fps}, 缓存={self.cache_size}, TTL={self.cache_ttl}")
            else:
                # 使用动态默认值
                self.max_fps = self._get_dynamic_default_fps()
                self.cache_size = 50
                self.cache_ttl = 0.1
                self.detection_history_length = 3
                self.movement_history_length = 5
                print("⚠️ [performance]节不存在，使用默认值")

        except Exception as e:
            print(f"❌ 解析performance设置失败: {e}")
            # 设置动态默认值
            self.max_fps = self._get_dynamic_default_fps()
            self.cache_size = 50
            self.cache_ttl = 0.1
            self.detection_history_length = 3
            self.movement_history_length = 5

    def _parse_compensation_settings(self) -> None:
        """Parse compensation settings."""
        try:
            value = self.parser.get('compensation', 'mode').lower()
            valid_modes = ['move', 'offset']
            if value in valid_modes:
                self.compensation_mode = value
            else:
                print('Warning: Invalid compensation mode')
                
            self.compensation_x = float(self.parser.get('compensation', 'compensation_x'))
            self.compensation_y = float(self.parser.get('compensation', 'compensation_y'))
            self.max_compensation_offset = float(self.parser.get('compensation', 'max_offset'))
            self.compensation_recovery = float(self.parser.get('compensation', 'recovery_rate'))

            # 延时压枪参数
            self.delay_before_compensation = int(self.parser.get('compensation', 'delay_before_compensation', fallback='0'))
            self.enhanced_compensation_multiplier = float(self.parser.get('compensation', 'enhanced_compensation_multiplier', fallback='2.5'))
            self.light_compensation_factor = float(self.parser.get('compensation', 'light_compensation_factor', fallback='0.3'))
            
        except Exception as e:
            print(f"Error parsing compensation settings: {e}")
            
    def _parse_action_settings(self) -> None:
        """Parse action timing settings."""
        try:
            self.action_delay = int(self.parser.get('action', 'action_delay'))
            self.action_randomization = int(self.parser.get('action', 'action_randomization'))
            self.action_threshold = int(self.parser.get('action', 'action_threshold'))
        except Exception as e:
            print(f"Error parsing action settings: {e}")
            
    def _parse_input_rate_settings(self) -> None:
        """Parse input rate settings."""
        try:
            self.target_input_rate = float(self.parser.get('input_rate', 'target_rate'))
        except Exception as e:
            print(f"Error parsing input rate settings: {e}")
            
    def _parse_key_bindings(self) -> None:
        """Parse key binding settings."""
        try:
            self.key_reload_settings = self._parse_hex_key(self.parser.get('keys', 'key_reload_settings'))
            self.key_toggle_enhancement = self._parse_hex_key(self.parser.get('keys', 'key_toggle_enhancement'))
            self.key_toggle_compensation = self._parse_hex_key(self.parser.get('keys', 'key_toggle_compensation'))
            self.key_system_exit = self._parse_hex_key(self.parser.get('keys', 'key_system_exit'))
            self.key_auto_action = self._parse_hex_key(self.parser.get('keys', 'key_auto_action'))
            self.key_rapid_input = self._parse_hex_key(self.parser.get('keys', 'key_rapid_input'))
            
            # Parse enhancement activation keys
            activation_keys_str = self.parser.get('keys', 'enhancement_activation_keys')
            if activation_keys_str.lower() != 'off':
                key_list = activation_keys_str.split(',')
                self.enhancement_activation_keys = [self._parse_hex_key(key.strip()) for key in key_list]
            else:
                self.enhancement_activation_keys = ['off']
                
        except Exception as e:
            print(f"Error parsing key bindings: {e}")
            
    def _parse_debug_settings(self) -> None:
        """Parse debug settings."""
        try:
            self.debug = self.parser.get('debug', 'enabled').lower() == 'true'
            self.debug_always_on = self.parser.get('debug', 'always_on').lower() == 'true'
            
            value = self.parser.get('debug', 'display_mode').lower()
            valid_modes = ['game', 'mask']
            if value in valid_modes:
                self.display_mode = value
            else:
                print('Warning: Invalid display_mode value')
                
        except Exception as e:
            print(f"Error parsing debug settings: {e}")
            
    @staticmethod
    def _parse_hex_key(key_string: str) -> int:
        """Parse hexadecimal key code."""
        return int(key_string, 16)
        
    def _create_default_settings(self) -> None:
        """Create default settings if configuration file is missing."""
        print("Creating default settings...")
        # Set reasonable defaults
        self.input_method = 'winapi'
        self.movement_smoothing_factor = 0.8
        self.movement_speed = 1.0
        self.vertical_speed_multiplier = 1.0
        # ... (other defaults)
        
    def reload_settings(self) -> None:
        """Reload settings from file."""
        self._load_settings()
        
    def save_settings(self) -> None:
        """Save current settings to file."""
        # Implementation for saving settings back to file
        pass

    def get_yolo_config(self):
        """Get YOLO configuration as an object."""
        class YOLOConfig:
            def __init__(self, settings):
                # Basic model settings
                self.model_type = settings.model_type
                self.model_format = settings.model_format
                self.model_path = settings.model_path

                # Enhanced model selection
                self.model_filename = settings.model_filename
                self.model_selection_strategy = settings.model_selection_strategy
                self.version_preference = settings.version_preference
                self.performance_priority = settings.performance_priority
                self.fallback_behavior = settings.fallback_behavior
                self.scan_subdirectories = settings.scan_subdirectories
                self.validate_models = settings.validate_models
                self.cache_model_info = settings.cache_model_info

                # Detection settings
                self.confidence_threshold = settings.confidence_threshold
                self.nms_threshold = settings.nms_threshold
                self.target_classes = settings.target_classes
                self.use_gpu = settings.use_gpu
                self.batch_size = settings.batch_size

                # Target selection and tracking
                self.target_priority = settings.target_priority
                self.max_target_distance = settings.max_target_distance
                self.position_threshold = settings.position_threshold
                self.smoothing_enabled = settings.smoothing_enabled
                self.smoothing_factor = settings.smoothing_factor

                # Anti-detection settings
                self.enable_anti_detection = settings.enable_anti_detection
                self.randomize_timing = settings.randomize_timing
                self.cache_randomization = settings.cache_randomization

        return YOLOConfig(self)

    def _parse_tyolo_optimization_settings(self) -> None:
        """Parse TYOLO optimization settings from [tyolo_optimization] section."""
        try:
            if self.parser.has_section('tyolo_optimization'):
                self.enable_tyolo_engine = self.parser.getboolean('tyolo_optimization', 'enable_tyolo_engine', fallback=False)
                self.use_multithreading = self.parser.getboolean('tyolo_optimization', 'use_multithreading', fallback=True)
                self.use_mss_capture = self.parser.getboolean('tyolo_optimization', 'use_mss_capture', fallback=True)
                self.use_tensorrt_engine = self.parser.getboolean('tyolo_optimization', 'use_tensorrt_engine', fallback=False)

                # 队列大小
                self.frame_queue_size = self.parser.getint('tyolo_optimization', 'frame_queue_size', fallback=5)
                self.detection_queue_size = self.parser.getint('tyolo_optimization', 'detection_queue_size', fallback=3)
                self.display_queue_size = self.parser.getint('tyolo_optimization', 'display_queue_size', fallback=2)

                print(f"✅ TYOLO优化配置已加载: 引擎={self.enable_tyolo_engine}")
            else:
                # Default TYOLO settings
                self.enable_tyolo_engine = False
                self.use_multithreading = True
                self.use_mss_capture = True
                self.use_tensorrt_engine = False
                self.frame_queue_size = 5
                self.detection_queue_size = 3
                self.display_queue_size = 2
        except Exception as e:
            print(f"Error parsing TYOLO optimization settings: {e}")
            # Set defaults
            self.enable_tyolo_engine = False
            self.use_multithreading = True
            self.use_mss_capture = True
            self.use_tensorrt_engine = False
            self.frame_queue_size = 5
            self.detection_queue_size = 3
            self.display_queue_size = 2

    def _parse_kmboxnet_settings(self) -> None:
        """Parse KmBoxNet network hardware settings."""
        try:
            if self.parser.has_section('kmboxnet'):
                self.kmboxnet_host = self.parser.get('kmboxnet', 'host', fallback='*************')
                self.kmboxnet_port = int(self.parser.get('kmboxnet', 'port', fallback='8888'))
            else:
                # 设置默认值
                self.kmboxnet_host = '*************'
                self.kmboxnet_port = 8888

        except Exception as e:
            print(f"Error parsing KmBoxNet settings: {e}")
            # 设置默认值
            self.kmboxnet_host = '*************'
            self.kmboxnet_port = 8888


