/*
  简化ARD控制器 - 专注基础移动功能
  适用于：<PERSON><PERSON><PERSON>o Leonardo
  功能：串口接收移动指令，执行鼠标移动和点击
*/

#include <Mouse.h>

// 串口通信配置
#define SERIAL_BAUD 115200
#define COMMAND_TIMEOUT 1

// 移动算法状态
struct MovementState {
  float smoothing_factor;
  float prev_x;
  float prev_y;
  bool initialized;
};

struct PIDState {
  float kp, ki, kd;
  float prev_error_x, prev_error_y;
  float integral_x, integral_y;
  unsigned long last_time;
  bool initialized;
};

// 全局状态
MovementState movement_state = {0.7, 0.0, 0.0, false};
PIDState pid_state = {1.2, 0.05, 0.3, 0.0, 0.0, 0.0, 0.0, 0, false};

// 命令处理
String command_buffer = "";
bool command_ready = false;

// 统计信息
unsigned long command_count = 0;
unsigned long last_status_time = 0;

void setup() {
  // 初始化串口
  Serial.begin(SERIAL_BAUD);
  Serial.setTimeout(COMMAND_TIMEOUT);
  
  // 初始化鼠标
  Mouse.begin();
  
  // 初始化随机数种子
  randomSeed(analogRead(0));
  
  // 初始化状态
  pid_state.last_time = millis();
  last_status_time = millis();
  
  // LED指示就绪
  pinMode(LED_BUILTIN, OUTPUT);
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_BUILTIN, HIGH);
    delay(200);
    digitalWrite(LED_BUILTIN, LOW);
    delay(200);
  }
  
  // 发送就绪信号
  Serial.println("SIMPLE_ARD_READY");
  Serial.println("Commands: M<x>,<y> | C | B1 | B0 | CONFIG_* | STATUS");
  Serial.flush();
}

void loop() {
  // 处理串口命令
  processSerialCommands();
  
  // 执行待处理的命令
  if (command_ready) {
    executeCommand(command_buffer);
    command_ready = false;
    command_buffer = "";
  }
  
  // 定期状态输出
  if (millis() - last_status_time > 30000) {  // 每30秒
    Serial.print("ARD Status: commands=");
    Serial.print(command_count);
    Serial.print(", uptime=");
    Serial.print(millis() / 1000);
    Serial.println("s");
    last_status_time = millis();
  }
  
  // LED心跳指示
  static unsigned long last_heartbeat = 0;
  if (millis() - last_heartbeat > 2000) {
    digitalWrite(LED_BUILTIN, HIGH);
    delay(50);
    digitalWrite(LED_BUILTIN, LOW);
    last_heartbeat = millis();
  }
  
  delay(1);
}

void processSerialCommands() {
  while (Serial.available() > 0) {
    char c = Serial.read();
    
    if (c == '\r' || c == '\n') {
      if (command_buffer.length() > 0) {
        command_ready = true;
        break;
      }
    } else {
      command_buffer += c;
    }
  }
}

void executeCommand(String cmd) {
  cmd.trim();
  
  if (cmd.length() == 0) {
    return;
  }
  
  command_count++;
  
  // 移动命令: M<x>,<y>
  if (cmd.startsWith("M")) {
    handleMoveCommand(cmd);
  }
  // 点击命令: C
  else if (cmd == "C") {
    handleClickCommand();
  }
  // 按下命令: B1
  else if (cmd == "B1") {
    Mouse.press(MOUSE_LEFT);
    sendResponse("OK");
  }
  // 释放命令: B0
  else if (cmd == "B0") {
    Mouse.release(MOUSE_LEFT);
    sendResponse("OK");
  }
  // 配置命令: CONFIG
  else if (cmd.startsWith("CONFIG")) {
    handleConfigCommand(cmd);
  }
  // 状态查询: STATUS
  else if (cmd == "STATUS") {
    sendStatusInfo();
  }
  // 测试命令: TEST
  else if (cmd == "TEST") {
    handleTestCommand();
  }
  else {
    sendResponse("ERROR_UNKNOWN_COMMAND");
  }
}

void handleMoveCommand(String cmd) {
  // 解析移动命令: M<x>,<y>
  int commaIndex = cmd.indexOf(',');
  if (commaIndex == -1) {
    sendResponse("ERROR_INVALID_FORMAT");
    return;
  }
  
  int raw_x = cmd.substring(1, commaIndex).toInt();
  int raw_y = cmd.substring(commaIndex + 1).toInt();
  
  // 应用移动算法
  float processed_x, processed_y;
  processMovement(raw_x, raw_y, &processed_x, &processed_y);
  
  // 执行移动
  executeMovement((int)processed_x, (int)processed_y);
  
  sendResponse("OK");
}

void processMovement(int raw_x, int raw_y, float* out_x, float* out_y) {
  float x = (float)raw_x;
  float y = (float)raw_y;
  
  // 1. 应用PID算法
  if (pid_state.initialized) {
    applyPIDControl(&x, &y);
  }
  
  // 2. 应用平滑算法
  if (movement_state.initialized) {
    x = applySmoothing(x, movement_state.prev_x);
    y = applySmoothing(y, movement_state.prev_y);
  }
  
  // 3. 应用人性化随机因子
  applyHumanization(&x, &y);
  
  // 更新状态
  movement_state.prev_x = x;
  movement_state.prev_y = y;
  movement_state.initialized = true;
  pid_state.initialized = true;
  
  *out_x = x;
  *out_y = y;
}

void applyPIDControl(float* x, float* y) {
  unsigned long current_time = millis();
  float dt = (current_time - pid_state.last_time) / 1000.0;
  
  if (dt <= 0) dt = 0.016;  // 假设60fps
  
  // PID计算 for X
  float error_x = *x;
  pid_state.integral_x += error_x * dt;
  pid_state.integral_x = constrain(pid_state.integral_x, -100.0, 100.0);
  float derivative_x = (error_x - pid_state.prev_error_x) / dt;
  
  *x = pid_state.kp * error_x + 
       pid_state.ki * pid_state.integral_x + 
       pid_state.kd * derivative_x;
  
  // PID计算 for Y
  float error_y = *y;
  pid_state.integral_y += error_y * dt;
  pid_state.integral_y = constrain(pid_state.integral_y, -100.0, 100.0);
  float derivative_y = (error_y - pid_state.prev_error_y) / dt;
  
  *y = pid_state.kp * error_y + 
       pid_state.ki * pid_state.integral_y + 
       pid_state.kd * derivative_y;
  
  // 更新状态
  pid_state.prev_error_x = error_x;
  pid_state.prev_error_y = error_y;
  pid_state.last_time = current_time;
  
  // 限制输出
  *x = constrain(*x, -127.0, 127.0);
  *y = constrain(*y, -127.0, 127.0);
}

float applySmoothing(float current, float previous) {
  return previous * movement_state.smoothing_factor + 
         current * (1.0 - movement_state.smoothing_factor);
}

void applyHumanization(float* x, float* y) {
  // 添加微小的随机偏移 (-1 到 +1)
  *x += (random(200) - 100) / 100.0;
  *y += (random(200) - 100) / 100.0;
}

void executeMovement(int x, int y) {
  // 限制移动范围
  x = constrain(x, -127, 127);
  y = constrain(y, -127, 127);
  
  // 执行移动
  Mouse.move(x, y, 0);
}

void handleClickCommand() {
  // 人性化点击：随机延迟
  int click_delay = random(40, 80);
  
  Mouse.press(MOUSE_LEFT);
  delay(click_delay);
  Mouse.release(MOUSE_LEFT);
  
  sendResponse("OK");
}

void handleConfigCommand(String cmd) {
  if (cmd.startsWith("CONFIG_PID_KP_")) {
    float value = cmd.substring(14).toFloat();
    pid_state.kp = constrain(value, 0.1, 5.0);
    sendResponse("OK");
  }
  else if (cmd.startsWith("CONFIG_PID_KI_")) {
    float value = cmd.substring(14).toFloat();
    pid_state.ki = constrain(value, 0.0, 1.0);
    sendResponse("OK");
  }
  else if (cmd.startsWith("CONFIG_PID_KD_")) {
    float value = cmd.substring(14).toFloat();
    pid_state.kd = constrain(value, 0.0, 2.0);
    sendResponse("OK");
  }
  else if (cmd.startsWith("CONFIG_SMOOTHING_")) {
    float value = cmd.substring(17).toFloat();
    movement_state.smoothing_factor = constrain(value, 0.1, 0.9);
    sendResponse("OK");
  }
  else {
    sendResponse("ERROR_UNKNOWN_CONFIG");
  }
}

void handleTestCommand() {
  Serial.println("TEST: Starting movement test...");
  
  // 测试移动序列
  int test_moves[][2] = {
    {10, 0}, {0, 10}, {-10, 0}, {0, -10},  // 方向测试
    {5, 5}, {-5, -5},                      // 对角测试
    {20, 0}, {-20, 0}                      // 大幅移动测试
  };
  
  for (int i = 0; i < 8; i++) {
    Serial.print("TEST: Move ");
    Serial.print(i + 1);
    Serial.print(" - (");
    Serial.print(test_moves[i][0]);
    Serial.print(", ");
    Serial.print(test_moves[i][1]);
    Serial.println(")");
    
    Mouse.move(test_moves[i][0], test_moves[i][1], 0);
    delay(500);
  }
  
  Serial.println("TEST: Movement test completed");
  sendResponse("TEST_OK");
}

void sendStatusInfo() {
  Serial.print("STATUS:");
  Serial.print("commands=");
  Serial.print(command_count);
  Serial.print(",pid_kp=");
  Serial.print(pid_state.kp);
  Serial.print(",smoothing=");
  Serial.print(movement_state.smoothing_factor);
  Serial.print(",uptime=");
  Serial.print(millis());
  Serial.println();
  Serial.flush();
}

void sendResponse(String response) {
  Serial.println(response);
  Serial.flush();
}
