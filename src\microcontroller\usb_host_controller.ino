/*
  USB Host控制器固件 - 借鉴Lainbot的HID设计
  支持高级算法处理和数据缓冲
  适用于Arduino Mega + USB Host Shield
*/

#include <SPI.h>
#include <Usb.h>
#include <usbhub.h>
#include <hiduniversal.h>
#include <HID-Project.h>
#include <HID-Settings.h>

// USB Host组件
USB          Usb;
USBHub       Hub(&Usb);
HIDUniversal Hid(&Usb);

// RawHID数据缓冲区
uint8_t rawBuf[64];
uint8_t responseBuf[64];

// 移动算法状态
struct AlgorithmState {
  float pid_kp;
  float pid_ki;
  float pid_kd;
  float prev_error_x;
  float prev_error_y;
  float integral_x;
  float integral_y;
  unsigned long last_time;
  bool initialized;
};

struct MovementBuffer {
  int16_t x_queue[16];
  int16_t y_queue[16];
  uint8_t queue_head;
  uint8_t queue_tail;
  uint8_t queue_size;
};

struct PerformanceStats {
  unsigned long total_commands;
  unsigned long successful_commands;
  unsigned long last_stats_time;
  float processing_time_avg;
};

// 全局状态
AlgorithmState algorithm_state = {1.2, 0.05, 0.3, 0.0, 0.0, 0.0, 0.0, 0, false};
MovementBuffer movement_buffer = {{0}, {0}, 0, 0, 0};
PerformanceStats perf_stats = {0, 0, 0, 0.0};

// 按钮掩码
const uint8_t buttonMasks[5] = { 0x01, 0x02, 0x04, 0x08, 0x10 };

// 自定义鼠标解析器
class EnhancedMouseParser : public HIDReportParser {
  uint8_t prevButtons = 0;

  void Parse(USBHID*, bool isRptID, uint8_t len, uint8_t *buf) override {
    uint8_t offset = isRptID ? 1 : 0;

    if (len < offset + 6) return;

    uint8_t buttons = buf[offset + 0];
    int8_t dx       = (int8_t)buf[offset + 1];
    int8_t dy       = (int8_t)buf[offset + 3];
    int8_t wheel    = (int8_t)buf[offset + 5];

    // 处理按钮状态变化
    for (uint8_t i = 0; i < 5; i++) {
      uint8_t mask = buttonMasks[i];
      bool now  = buttons & mask;
      bool prev = prevButtons & mask;

      if (now && !prev) BootMouse.press(mask);
      if (!now && prev) BootMouse.release(mask);
    }
    prevButtons = buttons;

    // 应用算法处理移动
    if (dx != 0 || dy != 0) {
      processMouseMovement(dx, dy);
    }

    // 处理滚轮
    if (wheel != 0) {
      BootMouse.move(0, 0, wheel);
    }
  }
} enhancedMouseParser;

void setup() {
  // 初始化串口
  Serial.begin(115200);
  
  // 初始化USB HID
  BootMouse.begin();
  RawHID.begin(rawBuf, sizeof(rawBuf));
  RawHID.enable();

  // 初始化USB Host
  if (Usb.Init() == -1) {
    Serial.println("ERROR: USB Host init failed");
    while (1);
  }
  
  // 设置鼠标解析器
  Hid.SetReportParser(0, &enhancedMouseParser);
  
  // 初始化算法状态
  algorithm_state.last_time = millis();
  perf_stats.last_stats_time = millis();
  
  Serial.println("USB_HOST_READY");
}

void loop() {
  // 处理USB Host任务
  Usb.Task();
  
  // 处理RawHID注入命令
  processRawHIDCommands();
  
  // 处理移动缓冲区
  processMovementBuffer();
  
  // 定期输出性能统计
  updatePerformanceStats();
}

void processRawHIDCommands() {
  int len = RawHID.read();
  if (len > 0) {
    unsigned long start_time = micros();
    
    perf_stats.total_commands++;
    
    // 解析命令
    uint8_t command_type = rawBuf[0];
    
    switch (command_type) {
      case 0x01: // 移动命令
        handleMoveCommand();
        break;
        
      case 0x02: // 点击命令
        handleClickCommand();
        break;
        
      case 0x03: // 配置命令
        handleConfigCommand();
        break;
        
      case 0x04: // 状态查询
        handleStatusQuery();
        break;
        
      default:
        sendErrorResponse("UNKNOWN_COMMAND");
        break;
    }
    
    // 更新性能统计
    unsigned long processing_time = micros() - start_time;
    updateProcessingTime(processing_time);
    
    RawHID.enable();
  }
}

void handleMoveCommand() {
  // 移动命令格式: [0x01, dx_low, dx_high, dy_low, dy_high]
  int16_t dx = (int16_t)((rawBuf[2] << 8) | rawBuf[1]);
  int16_t dy = (int16_t)((rawBuf[4] << 8) | rawBuf[3]);
  
  // 应用高级算法处理
  float processed_x, processed_y;
  applyAdvancedAlgorithm(dx, dy, &processed_x, &processed_y);
  
  // 添加到移动缓冲区
  addToMovementBuffer((int16_t)processed_x, (int16_t)processed_y);
  
  perf_stats.successful_commands++;
  sendSuccessResponse();
}

void handleClickCommand() {
  // 点击命令格式: [0x02, button_mask, delay_low, delay_high]
  uint8_t button_mask = rawBuf[1];
  uint16_t delay_ms = (rawBuf[3] << 8) | rawBuf[2];
  
  // 执行点击
  for (uint8_t i = 0; i < 5; i++) {
    uint8_t mask = buttonMasks[i];
    if (button_mask & mask) {
      BootMouse.press(mask);
      delay(delay_ms);
      BootMouse.release(mask);
    }
  }
  
  perf_stats.successful_commands++;
  sendSuccessResponse();
}

void handleConfigCommand() {
  // 配置命令格式: [0x03, param_id, value_bytes...]
  uint8_t param_id = rawBuf[1];
  
  switch (param_id) {
    case 0x01: // PID参数
      updatePIDParameters();
      break;
      
    case 0x02: // 缓冲区大小
      updateBufferSettings();
      break;
      
    default:
      sendErrorResponse("UNKNOWN_PARAM");
      return;
  }
  
  sendSuccessResponse();
}

void handleStatusQuery() {
  // 发送状态信息
  responseBuf[0] = 0x04; // 状态响应
  responseBuf[1] = (uint8_t)(perf_stats.total_commands & 0xFF);
  responseBuf[2] = (uint8_t)((perf_stats.total_commands >> 8) & 0xFF);
  responseBuf[3] = (uint8_t)(perf_stats.successful_commands & 0xFF);
  responseBuf[4] = (uint8_t)((perf_stats.successful_commands >> 8) & 0xFF);
  responseBuf[5] = movement_buffer.queue_size;
  responseBuf[6] = (uint8_t)(perf_stats.processing_time_avg);
  
  RawHID.send(responseBuf, 64);
}

void applyAdvancedAlgorithm(int16_t raw_x, int16_t raw_y, float* out_x, float* out_y) {
  unsigned long current_time = millis();
  float dt = (current_time - algorithm_state.last_time) / 1000.0;
  
  if (dt <= 0 || !algorithm_state.initialized) {
    dt = 0.016; // 假设60fps
    algorithm_state.initialized = true;
  }
  
  // PID算法处理
  float error_x = (float)raw_x;
  float error_y = (float)raw_y;
  
  // 积分项
  algorithm_state.integral_x += error_x * dt;
  algorithm_state.integral_y += error_y * dt;
  
  // 积分限制
  algorithm_state.integral_x = constrain(algorithm_state.integral_x, -100.0, 100.0);
  algorithm_state.integral_y = constrain(algorithm_state.integral_y, -100.0, 100.0);
  
  // 微分项
  float derivative_x = (error_x - algorithm_state.prev_error_x) / dt;
  float derivative_y = (error_y - algorithm_state.prev_error_y) / dt;
  
  // PID输出
  *out_x = (algorithm_state.pid_kp * error_x + 
           algorithm_state.pid_ki * algorithm_state.integral_x + 
           algorithm_state.pid_kd * derivative_x);
  
  *out_y = (algorithm_state.pid_kp * error_y + 
           algorithm_state.pid_ki * algorithm_state.integral_y + 
           algorithm_state.pid_kd * derivative_y);
  
  // 更新状态
  algorithm_state.prev_error_x = error_x;
  algorithm_state.prev_error_y = error_y;
  algorithm_state.last_time = current_time;
  
  // 输出限制
  *out_x = constrain(*out_x, -127.0, 127.0);
  *out_y = constrain(*out_y, -127.0, 127.0);
}

void addToMovementBuffer(int16_t x, int16_t y) {
  if (movement_buffer.queue_size < 16) {
    movement_buffer.x_queue[movement_buffer.queue_tail] = x;
    movement_buffer.y_queue[movement_buffer.queue_tail] = y;
    
    movement_buffer.queue_tail = (movement_buffer.queue_tail + 1) % 16;
    movement_buffer.queue_size++;
  }
}

void processMovementBuffer() {
  if (movement_buffer.queue_size > 0) {
    int16_t x = movement_buffer.x_queue[movement_buffer.queue_head];
    int16_t y = movement_buffer.y_queue[movement_buffer.queue_head];
    
    // 执行移动
    BootMouse.move((int8_t)constrain(x, -127, 127), 
                   (int8_t)constrain(y, -127, 127), 0);
    
    movement_buffer.queue_head = (movement_buffer.queue_head + 1) % 16;
    movement_buffer.queue_size--;
  }
}

void processMouseMovement(int8_t dx, int8_t dy) {
  // 处理来自真实鼠标的移动
  float processed_x, processed_y;
  applyAdvancedAlgorithm(dx, dy, &processed_x, &processed_y);
  
  // 直接执行移动（不使用缓冲区）
  BootMouse.move((int8_t)processed_x, (int8_t)processed_y, 0);
}

void updatePIDParameters() {
  // 从rawBuf读取新的PID参数
  algorithm_state.pid_kp = *((float*)&rawBuf[2]);
  algorithm_state.pid_ki = *((float*)&rawBuf[6]);
  algorithm_state.pid_kd = *((float*)&rawBuf[10]);
  
  // 重置PID状态
  algorithm_state.prev_error_x = 0.0;
  algorithm_state.prev_error_y = 0.0;
  algorithm_state.integral_x = 0.0;
  algorithm_state.integral_y = 0.0;
}

void updateBufferSettings() {
  // 缓冲区设置更新（预留功能）
}

void updatePerformanceStats() {
  unsigned long current_time = millis();
  
  // 每5秒输出一次性能统计
  if (current_time - perf_stats.last_stats_time > 5000) {
    float success_rate = (float)perf_stats.successful_commands / 
                        max(1UL, perf_stats.total_commands) * 100.0;
    
    Serial.print("PERF: commands=");
    Serial.print(perf_stats.total_commands);
    Serial.print(", success=");
    Serial.print(success_rate);
    Serial.print("%, avg_time=");
    Serial.print(perf_stats.processing_time_avg);
    Serial.print("us, buffer=");
    Serial.println(movement_buffer.queue_size);
    
    perf_stats.last_stats_time = current_time;
  }
}

void updateProcessingTime(unsigned long processing_time) {
  // 计算处理时间的移动平均
  perf_stats.processing_time_avg = perf_stats.processing_time_avg * 0.9 + 
                                  processing_time * 0.1;
}

void sendSuccessResponse() {
  responseBuf[0] = 0xFF; // 成功标志
  RawHID.send(responseBuf, 64);
}

void sendErrorResponse(const char* error_msg) {
  responseBuf[0] = 0x00; // 错误标志
  strncpy((char*)&responseBuf[1], error_msg, 63);
  RawHID.send(responseBuf, 64);
}
