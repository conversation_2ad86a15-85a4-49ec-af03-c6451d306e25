#!/usr/bin/env python3
"""
GitHub风格控制器 - 完全模仿Overwatcheat项目的通信方式
特点：同步写入 + 单向通信 + 不等待响应
"""

import serial
import time
import threading
from typing import Optional

class GitHubStyleController:
    """GitHub风格ARD控制器 - 模仿Overwatcheat项目"""
    
    def __init__(self, config):
        # 从配置中获取参数
        self.serial_port = getattr(config, 'ard_serial_port', 'COM18')
        self.baud_rate = getattr(config, 'ard_baud_rate', 115200)
        self.config = config
        
        # 串口设备
        self.port: Optional[serial.Serial] = None
        self.device_lock = threading.Lock()
        
        # 统计信息
        self.command_count = 0
        self.error_count = 0
        self.last_performance_log = time.time()
        
        # 初始化设备
        self._open_port()
    
    def _open_port(self):
        """打开串口 - 完全模仿GitHub项目的方式"""
        try:
            if self.port is None or not self.port.is_open:
                self.port = serial.Serial()
                
                # 设置串口参数 - 完全按照GitHub项目
                self.port.port = self.serial_port
                self.port.baudrate = self.baud_rate
                self.port.bytesize = serial.EIGHTBITS
                self.port.stopbits = serial.STOPBITS_ONE
                self.port.parity = serial.PARITY_NONE
                self.port.timeout = None  # 无超时，完全阻塞
                self.port.write_timeout = None  # 无写超时
                
                # 打开端口
                self.port.open()
                
                print(f"✅ GitHub风格ARD设备已连接: {self.serial_port}")
                
                # 等待Arduino初始化
                time.sleep(2)
                
        except Exception as e:
            print(f"❌ GitHub风格ARD设备连接失败: {e}")
            self.port = None
    
    def send_cursor_movement(self, x: int, y: int) -> None:
        """发送光标移动命令 - 完全模仿GitHub项目"""
        if x == 0 and y == 0:
            return
        
        # 限制移动范围
        x = max(-127, min(127, x))
        y = max(-127, min(127, y))
        
        try:
            # 确保端口打开
            if self.port is None or not self.port.is_open:
                self._open_port()
            
            if self.port and self.port.is_open:
                with self.device_lock:
                    # 完全模仿GitHub项目的方式
                    cmd = f"M{x},{y}\n"
                                        
                    # 同步写入 + 刷新，不等待响应
                    self.port.write(cmd.encode())
                    self.port.flush()
                    
                    self.command_count += 1
                    
                    # 定期性能日志
                    self._log_performance()
            
        except Exception as e:
            print(f"❌ GitHub风格移动命令失败: {e}")
            self.error_count += 1
            # 尝试重新连接
            self._reconnect()
    
    def send_action_input(self, delay_before_action: float = 0) -> None:
        """发送动作输入命令 - 完全模仿GitHub项目"""
        if delay_before_action > 0:
            time.sleep(delay_before_action)
        
        try:
            # 确保端口打开
            if self.port is None or not self.port.is_open:
                self._open_port()
            
            if self.port and self.port.is_open:
                with self.device_lock:
                    # 完全模仿GitHub项目的方式
                    cmd = "C\n"
                    
                    # 同步写入 + 刷新，不等待响应
                    self.port.write(cmd.encode())
                    self.port.flush()
                    
                    self.command_count += 1
            
        except Exception as e:
            print(f"❌ GitHub风格点击命令失败: {e}")
            self.error_count += 1
            # 尝试重新连接
            self._reconnect()
    
    def _reconnect(self):
        """重新连接设备"""
        try:
            if self.port:
                self.port.close()
                self.port = None
            time.sleep(0.1)
            self._open_port()
        except Exception as e:
            print(f"❌ 重新连接失败: {e}")
    
    def _log_performance(self):
        """记录性能统计"""
        current_time = time.time()
        if current_time - self.last_performance_log > 10:
            error_rate = (self.error_count / max(1, self.command_count)) * 100
            print(f"📊 GitHub风格性能: 命令={self.command_count}, 错误率={error_rate:.1f}%")
            self.last_performance_log = current_time
    
    def test_movement(self):
        """测试移动功能"""
        print("🧪 开始GitHub风格移动测试...")
        
        if not self.port or not self.port.is_open:
            print("❌ 设备未连接")
            return False
        
        # 发送测试序列
        test_moves = [(10, 0), (0, 10), (-10, 0), (0, -10)]
        
        for x, y in test_moves:
            self.send_cursor_movement(x, y)
            time.sleep(0.2)  # 稍长间隔观察效果
        
        print("✅ GitHub风格移动测试完成")
        return True
    
    def close_connection(self) -> None:
        """关闭设备连接"""
        try:
            if self.port:
                self.port.close()
                self.port = None
                print("✅ GitHub风格ARD设备连接已关闭")
                
        except Exception as e:
            print(f"❌ 设备关闭错误: {e}")
    
    def test_connection(self) -> bool:
        """测试设备连接"""
        try:
            if self.port and self.port.is_open:
                # GitHub风格：只要能写入就认为连接正常
                return True
            else:
                self._open_port()
                return self.port is not None and self.port.is_open
                
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False
